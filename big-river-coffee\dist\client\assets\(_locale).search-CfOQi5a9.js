import{w as y}from"./with-props-ZU_SrDpf.js";import{j as e}from"./jsx-runtime-D5QEUsP9.js";import{r as v,F as P,L as o,u as k}from"./chunk-D4RADZKF-DNKU_WG6.js";import{k as b,s as g}from"./index-BoSbOAL5.js";import{u as m}from"./search-DOeYwaXi.js";import{I as S}from"./Image-BhFgnyoc.js";import{M as p}from"./Money-BAfw1cBI.js";function E({children:t,...r}){const s=v.useRef(null);return L(s),typeof t!="function"?null:e.jsx(P,{method:"get",...r,children:t({inputRef:s})})}function L(t){v.useEffect(()=>{function r(s){var n,a;s.key==="k"&&s.metaKey&&(s.preventDefault(),(n=t.current)==null||n.focus()),s.key==="Escape"&&((a=t.current)==null||a.blur())}return document.addEventListener("keydown",r),()=>{document.removeEventListener("keydown",r)}},[t])}function l({term:t,result:r,children:s}){return r!=null&&r.total?s({...r.items,term:t}):null}l.Articles=N;l.Pages=w;l.Products=F;l.Empty=R;function N({term:t,articles:r}){var s;return r!=null&&r.nodes.length?e.jsxs("div",{className:"search-result",children:[e.jsx("h2",{children:"Articles"}),e.jsx("div",{children:(s=r==null?void 0:r.nodes)==null?void 0:s.map(n=>{const a=m({baseUrl:`/blogs/${n.handle}`,trackingParams:n.trackingParameters,term:t});return e.jsx("div",{className:"search-results-item",children:e.jsx(o,{prefetch:"intent",to:a,children:n.title})},n.id)})}),e.jsx("br",{})]}):null}function w({term:t,pages:r}){var s;return r!=null&&r.nodes.length?e.jsxs("div",{className:"search-result",children:[e.jsx("h2",{children:"Pages"}),e.jsx("div",{children:(s=r==null?void 0:r.nodes)==null?void 0:s.map(n=>{const a=m({baseUrl:`/pages/${n.handle}`,trackingParams:n.trackingParameters,term:t});return e.jsx("div",{className:"search-results-item",children:e.jsx(o,{prefetch:"intent",to:a,children:n.title})},n.id)})}),e.jsx("br",{})]}):null}function F({term:t,products:r}){return r!=null&&r.nodes.length?e.jsxs("div",{className:"search-result",children:[e.jsx("h2",{children:"Products"}),e.jsx(b,{connection:r,children:({nodes:s,isLoading:n,NextLink:a,PreviousLink:c})=>{const d=s.map(i=>{var j,f;const h=m({baseUrl:`/products/${i.handle}`,trackingParams:i.trackingParameters,term:t}),u=(j=i==null?void 0:i.selectedOrFirstAvailableVariant)==null?void 0:j.price,x=(f=i==null?void 0:i.selectedOrFirstAvailableVariant)==null?void 0:f.image;return e.jsx("div",{className:"search-results-item",children:e.jsxs(o,{prefetch:"intent",to:h,children:[x&&e.jsx(S,{data:x,alt:i.title,width:50}),e.jsxs("div",{children:[e.jsx("p",{children:i.title}),e.jsx("small",{children:u&&e.jsx(p,{data:u})})]})]})},i.id)});return e.jsxs("div",{children:[e.jsx("div",{children:e.jsx(c,{children:n?"Loading...":e.jsx("span",{children:"↑ Load previous"})})}),e.jsxs("div",{children:[d,e.jsx("br",{})]}),e.jsx("div",{children:e.jsx(a,{children:n?"Loading...":e.jsx("span",{children:"Load more ↓"})})})]})}}),e.jsx("br",{})]}):null}function R(){return e.jsx("p",{children:"No results, try a different search."})}const M=()=>[{title:"Hydrogen | Search"}],O=y(function(){const{type:r,term:s,result:n,error:a}=k();return r==="predictive"?null:e.jsxs("div",{className:"search",children:[e.jsx("h1",{children:"Search"}),e.jsx(E,{children:({inputRef:c})=>e.jsxs(e.Fragment,{children:[e.jsx("input",{defaultValue:s,name:"q",placeholder:"Search…",ref:c,type:"search"})," ",e.jsx("button",{type:"submit",children:"Search"})]})}),a&&e.jsx("p",{style:{color:"red"},children:a}),!s||!(n!=null&&n.total)?e.jsx(l.Empty,{}):e.jsx(l,{result:n,term:s,children:({articles:c,pages:d,products:i,term:h})=>e.jsxs("div",{children:[e.jsx(l.Products,{products:i,term:h}),e.jsx(l.Pages,{pages:d,term:h}),e.jsx(l.Articles,{articles:c,term:h})]})}),e.jsx(g.SearchView,{data:{searchTerm:s,searchResults:n}})]})});export{O as default,M as meta};
