import "react-router";

declare module "react-router" {
  interface Register {
    params: Params;
  }

  interface Future {
    unstable_middleware: false
  }
}

type Params = {
  "/": {};
  "/robots.txt": {};
  "/:locale?": {
    "locale"?: string;
  };
  "/:locale?/blogs/:blogHandle/:articleHandle": {
    "locale"?: string;
    "blogHandle": string;
    "articleHandle": string;
  };
  "/:locale?/api/:version/graphql.json": {
    "locale"?: string;
    "version": string;
  };
  "/:locale?/sitemap/:type/:page.xml": {
    "locale"?: string;
    "type": string;
    "page": string;
  };
  "/:locale?/blogs/:blogHandle": {
    "locale"?: string;
    "blogHandle": string;
  };
  "/:locale?/collections/all-coffees": {
    "locale"?: string;
  };
  "/:locale?/products/roasters-box": {
    "locale"?: string;
  };
  "/:locale?/collections/:handle": {
    "locale"?: string;
    "handle": string;
  };
  "/:locale?/pages/subscriptions": {
    "locale"?: string;
  };
  "/:locale?/account/authorize": {
    "locale"?: string;
  };
  "/:locale?/collections": {
    "locale"?: string;
  };
  "/:locale?/account/register": {
    "locale"?: string;
  };
  "/:locale?/policies/:handle": {
    "locale"?: string;
    "handle": string;
  };
  "/:locale?/products/:handle": {
    "locale"?: string;
    "handle": string;
  };
  "/:locale?/account/logout": {
    "locale"?: string;
  };
  "/:locale?/collections/all": {
    "locale"?: string;
  };
  "/:locale?/pages/nicaragua": {
    "locale"?: string;
  };
  "/:locale?/policies": {
    "locale"?: string;
  };
  "/:locale?/account/login": {
    "locale"?: string;
  };
  "/:locale?/discount/:code": {
    "locale"?: string;
    "code": string;
  };
  "/:locale?/pages/:handle": {
    "locale"?: string;
    "handle": string;
  };
  "/:locale?/sitemap.xml": {
    "locale"?: string;
  };
  "/:locale?/blogs": {
    "locale"?: string;
  };
  "/:locale?/pages/brew": {
    "locale"?: string;
  };
  "/:locale?/affiliate": {
    "locale"?: string;
  };
  "/:locale?/our-story": {
    "locale"?: string;
  };
  "/:locale?/account": {
    "locale"?: string;
  };
  "/:locale?/account/orders": {
    "locale"?: string;
  };
  "/:locale?/account/orders/:id": {
    "locale"?: string;
    "id": string;
  };
  "/:locale?/account/addresses": {
    "locale"?: string;
  };
  "/:locale?/account/profile": {
    "locale"?: string;
  };
  "/:locale?/account/*": {
    "locale"?: string;
    "*": string;
  };
  "/:locale?/contact": {
    "locale"?: string;
  };
  "/:locale?/search": {
    "locale"?: string;
  };
  "/:locale?/cart": {
    "locale"?: string;
  };
  "/:locale?/cart/:lines": {
    "locale"?: string;
    "lines": string;
  };
  "/:locale?/*": {
    "locale"?: string;
    "*": string;
  };
};