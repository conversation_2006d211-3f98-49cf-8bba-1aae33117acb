import{j as t}from"./jsx-runtime-D5QEUsP9.js";import{j as s}from"./index-BVkVvY9o.js";function m({analytics:i,children:r,disabled:n,lines:a,onClick:o,className:l}){return t.jsx(s,{route:"/cart",inputs:{lines:a},action:s.ACTIONS.LinesAdd,children:e=>t.jsxs(t.Fragment,{children:[t.jsx("input",{name:"analytics",type:"hidden",value:JSON.stringify(i)}),t.jsx("button",{type:"submit",onClick:o,disabled:n??e.state!=="idle",className:l||"bg-army-600 hover:bg-army-700 text-white py-2 px-4 rounded-lg font-medium transition-colors duration-200",children:e.state!=="idle"?t.jsxs("div",{className:"flex items-center justify-center",children:[t.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[t.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),t.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Adding..."]}):r})]})})}export{m as A};
