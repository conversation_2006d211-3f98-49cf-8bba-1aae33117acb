import{w as Ue}from"./with-props-ZU_SrDpf.js";import{j as e}from"./jsx-runtime-D5QEUsP9.js";import{u as Qe,b as Je,c as Ze,r as d,d as Xe}from"./chunk-D4RADZKF-DNKU_WG6.js";import{P as et}from"./ProductCard-Og2ifzIn.js";import{A as Ve}from"./AddToCartButton-C9QeiPEA.js";import{u as tt}from"./Aside-HLZLr7KT.js";import{O as st}from"./OptimizedVideo-C7WWprUQ.js";import{i as rt}from"./scrollAnimations-D2F8dg76.js";import{M as at}from"./Money-BAfw1cBI.js";import{a as ot}from"./getProductOptions-DiFA5z_h.js";import"./variants-J2GcD6hP.js";import"./Image-BhFgnyoc.js";import"./index-BX9xgbR8.js";const Lt=()=>[{title:"All Coffees | Big River Coffee"},{description:"Discover our complete collection of premium coffee - ethically sourced and expertly roasted for every adventure"}],Ft=Ue(function(){const{products:U}=Qe(),[k]=Je(),ze=Ze(),[it,lt]=d.useState(U.nodes),[Q,ye]=d.useState("grid"),[ve,Ee]=d.useState("featured"),[ct,dt]=d.useState(!1),[ut,mt]=d.useState(!0),[w,J]=d.useState("coffee"),[Z,X]=d.useState(!1),[ht,xt]=d.useState(!1),ee=d.useRef(null),te=d.useRef(null),se=d.useRef(null),re=d.useRef(null),je=k.get("roast")||"all",we=k.get("kcup-type")||"all",Ne=k.get("gear-category")||"all",ke=k.get("price")||"all",Ce=k.get("section")||"coffee",de=t=>{const a=t.title.toLowerCase(),l=t.tags||[];return a.includes("k-cup")||a.includes("kcup")||a.includes("k cup")||l.some(n=>n.toLowerCase().includes("k-cup"))?"kcups":a.includes("mug")||a.includes("cup")||a.includes("tumbler")||a.includes("grinder")||a.includes("equipment")||a.includes("gear")||a.includes("shirt")||a.includes("hat")||a.includes("apparel")||l.some(n=>{const r=n.toLowerCase();return r.includes("gear")||r.includes("equipment")||r.includes("apparel")||r.includes("accessories")})?"gear":"coffee"},E={coffee:U.nodes.filter(t=>de(t)==="coffee"),kcups:U.nodes.filter(t=>de(t)==="kcups"),gear:U.nodes.filter(t=>de(t)==="gear")},qe=(t,a)=>{let l=[...a];switch(t==="coffee"&&je!=="all"?l=l.filter(n=>n.title.toLowerCase().includes(je)):t==="kcups"&&we!=="all"?l=l.filter(n=>{const r=n.title.toLowerCase();switch(we){case"single-origin":return r.includes("single")||r.includes("origin");case"blend":return r.includes("blend");case"flavored":return r.includes("vanilla")||r.includes("caramel")||r.includes("hazelnut")||r.includes("flavored");default:return!0}}):t==="gear"&&Ne!=="all"&&(l=l.filter(n=>{const r=n.title.toLowerCase();switch(Ne){case"mugs":return r.includes("mug")||r.includes("cup")||r.includes("tumbler");case"equipment":return r.includes("grinder")||r.includes("equipment")||r.includes("machine");case"apparel":return r.includes("shirt")||r.includes("hat")||r.includes("apparel");case"accessories":return r.includes("accessory")||r.includes("gear")||!r.includes("mug")&&!r.includes("cup")&&!r.includes("grinder")&&!r.includes("shirt");default:return!0}})),ke!=="all"&&(l=l.filter(n=>{const r=parseFloat(n.priceRange.minVariantPrice.amount);switch(ke){case"under-25":return r<25;case"25-50":return r>=25&&r<=50;case"over-50":return r>50;default:return!0}})),ve){case"price-low":l.sort((n,r)=>parseFloat(n.priceRange.minVariantPrice.amount)-parseFloat(r.priceRange.minVariantPrice.amount));break;case"price-high":l.sort((n,r)=>parseFloat(r.priceRange.minVariantPrice.amount)-parseFloat(n.priceRange.minVariantPrice.amount));break;case"name":l.sort((n,r)=>n.title.localeCompare(r.title));break}return l};d.useEffect(()=>{Z||J(Ce)},[Ce,Z]),d.useEffect(()=>{const t=k.get("section");t&&["coffee","kcups","subscriptions","gear"].includes(t)&&(J(t),setTimeout(()=>{const a=t==="coffee"?ee:t==="kcups"?te:t==="subscriptions"?se:re;if(a.current){const q=a.current.offsetTop,O=Math.max(0,q-188);window.scrollTo({top:O,behavior:"smooth"})}},300))},[k]),d.useEffect(()=>{let t,a=!1;const l=()=>{Z||a||(a=!0,clearTimeout(t),t=setTimeout(()=>{try{const n=[{name:"coffee",ref:ee},{name:"kcups",ref:te},{name:"subscriptions",ref:se},{name:"gear",ref:re}],r=window.pageYOffset,I=120-32+80+20;let S=w;for(const o of n)if(o.ref.current){const v=o.ref.current.offsetTop-I,p=v+o.ref.current.offsetHeight;if(r>=v&&r<p){S=o.name;break}}if(S!==w){J(S);const o=new URLSearchParams(k);o.set("section",S),window.history.replaceState({},"",`?${o.toString()}`)}}catch(n){console.error("Scroll handling error:",n)}finally{a=!1}},150))};return window.addEventListener("scroll",l,{passive:!0}),()=>{window.removeEventListener("scroll",l),clearTimeout(t)}},[w,k,Z]),d.useEffect(()=>{const t=()=>{const r=document.querySelector(".collections-mobile-nav");r&&(r.style.display=window.innerWidth<1024?"block":"none",r.style.visibility="visible");const L=document.querySelector(".collections-desktop-nav");L&&(L.style.display=window.innerWidth>=1024?"flex":"none",L.style.visibility="visible")},a=setTimeout(t,10),l=setTimeout(t,100),n=setTimeout(t,500);return window.addEventListener("resize",t),document.addEventListener("visibilitychange",t),()=>{clearTimeout(a),clearTimeout(l),clearTimeout(n),window.removeEventListener("resize",t),document.removeEventListener("visibilitychange",t)}},[]);const Ie=t=>{Ee(t)},Se=t=>{try{X(!0),J(t);const l={coffee:ee,kcups:te,subscriptions:se,gear:re}[t];if(l.current){const O=l.current.offsetTop,I=Math.max(0,O-188),S=new URLSearchParams(k);S.set("section",t),window.history.replaceState({},"",`?${S.toString()}`),window.scrollTo({top:I,behavior:"smooth"}),setTimeout(()=>{X(!1)},1200)}else console.warn(`Section ref not found for: ${t}`),X(!1)}catch(a){console.error("Error in scrollToSection:",a),X(!1)}},ue=t=>{const a={coffee:E.coffee.length,kcups:E.kcups.length,subscriptions:1,gear:E.gear.length};return{coffee:{title:"Coffee",description:"Premium coffee beans, ethically sourced and expertly roasted",icon:e.jsxs("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{d:"M2 21h18v-2H2v2zM20 8h-2V5c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v8c0 2.21 1.79 4 4 4h6c2.21 0 4-1.79 4-4v-3h2c1.1 0 2-.9 2-2s-.9-2-2-2zM16 13c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2V5h12v8z"}),e.jsx("path",{d:"M20 10c.55 0 1-.45 1-1s-.45-1-1-1v2z"})]}),count:a.coffee},kcups:{title:"K-Cups",description:"Convenient single-serve coffee pods for your Keurig",icon:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),count:a.kcups},gear:{title:"Gear",description:"Coffee equipment, mugs, and adventure gear",icon:e.jsxs("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),count:a.gear},subscriptions:{title:"Subscriptions",description:"Never run out of coffee with our flexible subscription service",icon:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),count:a.subscriptions}}[t]};return d.useEffect(()=>{if(!(typeof window>"u"))return document.body.classList.add("collections-page"),()=>{document.body.classList.remove("collections-page")}},[]),d.useEffect(()=>rt(),[]),e.jsxs(e.Fragment,{children:[e.jsx("style",{children:`
        body.collections-page {
          margin: 0 !important;
          padding: 0 !important;
        }
        body.collections-page main {
          padding: 0 !important;
          margin: 0 !important;
        }
      `}),e.jsx("div",{className:"hidden sm:block sticky top-0 w-full h-screen z-0",children:e.jsx("div",{className:"w-full h-full",children:e.jsx(st,{src:"/newhomepage/shop_hero_vid.mp4",poster:"/newhomepage/shop_stillframe.webp",className:"w-full h-full object-cover",style:{width:"100%",height:"100%",display:"block"},autoPlay:!0,muted:!0,loop:!0,playsInline:!0,preload:"auto",lazy:!1})})}),e.jsxs("div",{className:"relative z-10 min-h-screen bg-gray-50",style:{marginTop:"0",paddingTop:"0"},children:[e.jsx("div",{className:"sticky z-40 bg-white border-b border-neutral-200 shadow-sm top-0",children:e.jsx("div",{className:"container-clean",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between py-4 gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"hidden lg:flex space-x-2",children:["coffee","kcups","subscriptions","gear"].map(t=>{const a=ue(t),l=w===t;return e.jsxs("button",{type:"button",onClick:n=>{n.preventDefault(),n.stopPropagation(),Se(t)},className:`relative flex items-center px-4 py-2 rounded-lg font-semibold transition-all duration-300 ease-out whitespace-nowrap transform hover:scale-105 hover:-translate-y-0.5 ${l?"bg-army-600 text-white shadow-lg hover:bg-army-700":"text-neutral-600 hover:text-army-600 hover:bg-army-50 hover:shadow-md"}`,children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:a.icon}),e.jsx("span",{className:"mr-3",children:a.title}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full font-medium flex-shrink-0 ${l?"bg-white/20 text-white":"bg-army-100 text-army-800"}`,children:a.count})]},`desktop-${t}`)})}),e.jsx("div",{className:"lg:hidden",children:e.jsx("div",{className:"grid grid-cols-2 gap-3",children:["coffee","kcups","subscriptions","gear"].map(t=>{const a=ue(t),l=w===t;return e.jsxs("button",{type:"button",onClick:n=>{n.preventDefault(),n.stopPropagation(),Se(t)},className:`flex items-center justify-center px-3 py-3 rounded-lg font-medium transition-all duration-300 ease-out transform hover:scale-105 ${l?"bg-army-600 text-white shadow-lg hover:bg-army-700":"bg-white text-neutral-700 border border-neutral-200 hover:border-army-300 hover:text-army-600 hover:shadow-md"}`,children:[e.jsx("span",{className:"mr-2 flex-shrink-0",children:a.icon}),e.jsx("span",{className:"text-sm font-semibold",children:a.title})]},`mobile-${t}`)})})})]}),e.jsxs("div",{className:"hidden lg:flex items-center space-x-4",children:[e.jsxs("select",{value:ve,onChange:t=>Ie(t.target.value),className:"border border-neutral-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-army-500 min-w-[140px] transition-all duration-300 ease-out hover:border-army-400 hover:shadow-md cursor-pointer",children:[e.jsx("option",{value:"featured",children:"Featured"}),e.jsx("option",{value:"name",children:"Name A-Z"}),e.jsx("option",{value:"price-low",children:"Price: Low to High"}),e.jsx("option",{value:"price-high",children:"Price: High to Low"})]}),e.jsxs("div",{className:"flex items-center border border-neutral-300 rounded-lg overflow-hidden bg-white shadow-sm",children:[e.jsx("button",{onClick:()=>ye("grid"),className:`relative flex items-center justify-center w-12 h-12 transition-all duration-300 ease-out transform hover:scale-105 ${Q==="grid"?"bg-army-600 text-white shadow-md hover:bg-army-700":"bg-white text-neutral-600 hover:bg-army-50 hover:text-army-600 hover:shadow-md"}`,title:"Grid View","aria-label":"Grid View",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2,children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})})}),e.jsx("div",{className:"w-px h-6 bg-neutral-300"}),e.jsx("button",{onClick:()=>ye("list"),className:`relative flex items-center justify-center w-12 h-12 transition-all duration-300 ease-out transform hover:scale-105 ${Q==="list"?"bg-army-600 text-white shadow-md hover:bg-army-700":"bg-white text-neutral-600 hover:bg-army-50 hover:text-army-600 hover:shadow-md"}`,title:"List View","aria-label":"List View",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2,children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})})})]})]})]})})}),e.jsxs("div",{className:"container-clean py-12",children:[e.jsx("div",{className:"flex flex-col lg:flex-row gap-8",children:e.jsx("div",{className:"flex-1",children:["coffee","kcups","subscriptions","gear"].map(t=>{var r,L,D,q,O,I,S;const a=ue(t),l=t==="subscriptions"?[]:E[t],n=t==="subscriptions"?[]:qe(t,l);if(t==="kcups"){const o=n[0];if(o&&n.length>0){const v=((r=o.variants)==null?void 0:r.nodes)||[],p=v.filter(h=>h.availableForSale),K=p[0]||v[0];if(!K)return null;const[m,T]=d.useState(K),[x,F]=d.useState(1),R=h=>{var W;if(h.title&&h.title!=="Default Title")return h.title;const b=(W=h.selectedOptions)==null?void 0:W.find(j=>j.name.toLowerCase().includes("flavor")||j.name.toLowerCase().includes("title")||j.name.toLowerCase().includes("variant")||j.name.toLowerCase().includes("type"));return b!=null&&b.value?b.value:h.title||`K-Cup Flavor ${h.id.split("/").pop()}`};return e.jsxs("div",{ref:te,className:"mb-20",id:`section-${t}`,style:{scrollMarginTop:"188px"},children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-army-100 rounded-xl flex items-center justify-center mr-3 text-army-600",children:a.icon}),e.jsxs("div",{children:[e.jsx("h2",{className:`text-3xl font-bold transition-colors duration-300 ease-out ${w===t?"text-army-700":"text-gray-900"}`,style:{fontFamily:"var(--font-title)"},children:a.title}),e.jsx("p",{className:"text-gray-600",children:a.description})]})]})}),e.jsx("div",{className:`h-1 rounded-full transition-all duration-300 ease-out ${w===t?"bg-army-600 w-24":"bg-gray-200 w-16"}`})]}),e.jsx("div",{className:"bg-gradient-to-r from-army-50 to-army-100 rounded-xl border border-army-200 p-4 sm:p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8",children:[e.jsx("div",{className:"kcup-image-container aspect-square bg-white rounded-xl overflow-hidden shadow-sm max-w-sm mx-auto lg:max-w-md lg:mx-0",children:o.featuredImage?e.jsx("img",{src:o.featuredImage.url,alt:o.featuredImage.altText||o.title,className:"w-full h-full object-contain p-4"}):e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsx("svg",{className:"w-16 h-16 sm:w-24 sm:h-24 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})})}),e.jsxs("div",{className:"flex flex-col justify-between space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl sm:text-2xl font-bold text-gray-900 mb-2",style:{fontFamily:"var(--font-title)"},children:"Big River K-Cups"}),e.jsx("p",{className:"text-sm sm:text-base text-gray-600 mb-4",children:"20 count single-serve pods • Compatible with Keurig 1.0 & 2.0"}),e.jsx("div",{className:"text-2xl sm:text-3xl font-bold text-army-700 mb-4 sm:mb-6",children:e.jsx(at,{data:o.priceRange.minVariantPrice})}),e.jsxs("div",{className:"mb-4 sm:mb-6",children:[e.jsx("label",{className:"text-base sm:text-lg font-semibold text-gray-900 mb-3 block",children:"Flavors"}),p.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3",children:p.map(h=>{const b=R(h);return e.jsx("button",{onClick:()=>T(h),className:`px-3 sm:px-4 py-3 border rounded-lg text-xs sm:text-sm font-medium transition-all duration-300 ease-out text-left min-h-[48px] transform hover:scale-105 hover:-translate-y-0.5 ${(m==null?void 0:m.id)===h.id?"border-army-600 bg-army-600 text-white shadow-md hover:bg-army-700 hover:shadow-lg":"border-gray-300 bg-white text-gray-700 hover:border-army-500 hover:text-army-600 hover:bg-army-50 hover:shadow-md"}`,children:b},h.id)})}):v.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3",children:v.map(h=>{const b=R(h);return e.jsxs("button",{onClick:()=>T(h),disabled:!h.availableForSale,className:`px-3 sm:px-4 py-3 border rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 text-left min-h-[48px] ${(m==null?void 0:m.id)===h.id?"border-amber-600 bg-amber-600 text-white shadow-md":h.availableForSale?"border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600":"border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed"}`,children:[b," ",!h.availableForSale&&"(Out of Stock)"]},h.id)})}):e.jsx("div",{className:"text-gray-500 text-sm",children:"No flavor options available"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between sm:justify-start sm:space-x-4",children:[e.jsx("span",{className:"text-base sm:text-lg font-semibold text-gray-900",children:"Quantity:"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>F(Math.max(1,x-1)),className:"w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 hover:bg-army-50 transition-all duration-300 ease-out transform hover:scale-105 min-h-[44px] min-w-[44px]",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4"})})}),e.jsx("span",{className:"text-lg sm:text-xl font-semibold text-gray-900 min-w-[3rem] text-center",children:x}),e.jsx("button",{onClick:()=>F(x+1),className:"w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 hover:bg-army-50 transition-all duration-300 ease-out transform hover:scale-105 min-h-[44px] min-w-[44px]",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})})]})]}),e.jsx(Ve,{disabled:!m||!m.availableForSale||!m.id,lines:m!=null&&m.id?[{merchandiseId:m.id,quantity:x}]:[],className:"w-full bg-army-600 hover:bg-army-700 hover:scale-105 disabled:bg-gray-400 disabled:cursor-not-allowed disabled:hover:scale-100 text-white py-3 sm:py-4 px-4 sm:px-6 rounded-lg font-semibold text-base sm:text-lg transition-all duration-300 ease-out min-h-[48px] hover:shadow-lg",children:m?m.availableForSale?"Add to cart":"Out of stock":"Select a flavor"})]})]})]})})]},t)}}if(t==="subscriptions"){const[o,v]=d.useState(""),[p,K]=d.useState(""),[m,T]=d.useState(""),[x,F]=d.useState(""),[R,h]=d.useState(""),[b,W]=d.useState(""),[j,me]=d.useState(""),[M,he]=d.useState(""),[ae,Pe]=d.useState(1),[oe,We]=d.useState("monthly"),[xe,fe]=d.useState(!1),ge=d.useRef(null),u=Xe(),{open:Ae}=tt();d.useEffect(()=>{const s=f=>{ge.current&&!ge.current.contains(f.target)&&fe(!1)};return document.addEventListener("mousedown",s),()=>{document.removeEventListener("mousedown",s)}},[]);const De=()=>E.coffee||[],Ke=(s,f=[])=>{const y=new FormData;y.append("action","fetchProduct"),y.append("handle",s),y.append("selectedOptions",JSON.stringify(f)),u.submit(y,{method:"POST"})},Ge=()=>{var s;return((s=E.kcups)==null?void 0:s[0])||null},Le=s=>{var f,y;if(!s)return[];try{return ot({...s,selectedOrFirstAvailableVariant:s.selectedOrFirstAvailableVariant||((y=(f=s.variants)==null?void 0:f.nodes)==null?void 0:y[0])})}catch(C){return console.error("Error getting product options:",C),[]}},Ye=(s,f)=>{var y;return(y=s==null?void 0:s.variants)!=null&&y.nodes?s.variants.nodes.find(C=>Object.entries(f).every(([$,N])=>{var H;return(H=C.selectedOptions)==null?void 0:H.some(c=>c.name.toLowerCase()===$.toLowerCase()&&c.value===N)})):null},Fe=s=>{v(s),K(""),T(""),F(""),h(""),W(""),me(""),he("")},Me=De(),ne=Ge(),G=Me.find(s=>s.id===p);d.useEffect(()=>{p&&G&&(T(""),F(""),Ke(G.handle,[]))},[p]);const ie=[{value:"weekly",label:"1 Week",sellingPlanId:"gid://shopify/SellingPlan/9581953339"},{value:"monthly",label:"1 Month",sellingPlanId:"gid://shopify/SellingPlan/9581986107"},{value:"3weeks",label:"3 Weeks",sellingPlanId:"gid://shopify/SellingPlan/9582018875"},{value:"6weeks",label:"6 Weeks",sellingPlanId:"gid://shopify/SellingPlan/9582051643"}],_e=ie.find(s=>s.value===oe)||ie[1];return e.jsxs("div",{ref:se,className:"mb-20",id:`section-${t}`,style:{scrollMarginTop:"188px"},children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 bg-army-100 rounded-xl flex items-center justify-center mr-3 text-army-600",children:a.icon}),e.jsxs("div",{children:[e.jsx("h2",{className:`text-3xl font-bold transition-colors duration-200 ${w===t?"text-amber-600":"text-gray-900"}`,style:{fontFamily:"var(--font-title)"},children:"Big River Coffee Subscriptions"}),e.jsx("p",{className:"text-gray-600",children:"Never run out of coffee with our flexible subscription service"})]})]})}),e.jsx("div",{className:`h-1 rounded-full transition-all duration-300 ${w===t?"bg-amber-600 w-24":"bg-gray-200 w-16"}`})]}),e.jsx("div",{className:"bg-gradient-to-r from-army-50 to-army-100 rounded-xl border border-army-200 p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsx("div",{className:"aspect-square bg-white rounded-xl overflow-hidden shadow-sm",children:e.jsx("img",{src:"/subscriptionimg.webp",alt:"Big River Coffee Subscription",className:"w-full h-full object-center object-cover"})}),e.jsxs("div",{className:"flex flex-col justify-between",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900",style:{fontFamily:"var(--font-title)"},children:"Big River Coffee Subscription"}),e.jsx("span",{className:"bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold",children:"15% OFF"})]}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Fresh coffee delivered to your doorstep • Save 15% on every order • Free shipping on orders over $30"}),e.jsxs("div",{className:"mb-6 space-y-2",children:[e.jsxs("div",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-army-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Save your money with up to 15% off"]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-army-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Delay, modify & cancel anytime"]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-700",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-army-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Simplify your month with automatic delivery"]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Choose Your Product"}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsx("button",{onClick:()=>Fe("coffee"),className:`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200 text-left ${o==="coffee"?"border-army-600 bg-army-600 text-white shadow-md":"border-gray-300 bg-white text-gray-700 hover:border-army-500 hover:text-army-600"}`,children:"Coffee Beans"}),e.jsx("button",{onClick:()=>Fe("kcups"),className:`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200 text-left ${o==="kcups"?"border-amber-600 bg-amber-600 text-white shadow-md":"border-gray-300 bg-white text-gray-700 hover:border-amber-500 hover:text-amber-600"}`,children:"K-Cups"})]})]}),o==="coffee"&&e.jsxs("div",{className:"mb-6 transition-all duration-200",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Select Coffee Product"}),e.jsx("div",{className:"relative",children:e.jsxs("select",{value:p,onChange:s=>{K(s.target.value),T(""),F(""),W(""),me(""),he("")},className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose a coffee..."}),Me.map(s=>e.jsx("option",{value:s.id,children:s.title},s.id))]})})]}),o==="coffee"&&p&&e.jsx("div",{className:"mb-6 transition-all duration-200",children:u.state==="submitting"||u.state==="loading"?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"}),e.jsx("span",{className:"ml-3 text-gray-600",children:"Loading product options..."})]}):(L=u.data)!=null&&L.product?e.jsx("div",{className:"space-y-4",children:(()=>{var H;const s=Le(u.data.product),f=u.data.product.id==="gid://shopify/Product/8965076156731"||u.data.product.title.toLowerCase().includes("build your own"),y=u.data.product.id==="gid://shopify/Product/10111589941563"||u.data.product.title.toLowerCase().includes("blend box"),C=u.data.product.id==="gid://shopify/Product/10111587647803"||u.data.product.title.toLowerCase().includes("roasters box");if(f||s.some(c=>c.name.includes("Flavor #"))){const c=s.find(i=>i.name==="Flavor #1"),g=((H=c==null?void 0:c.optionValues)==null?void 0:H.map(i=>i.name))||[];return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Build Your Own Bundle"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Pick 3 flavors for your custom coffee bundle"})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Flavor #1"}),e.jsxs("select",{value:b,onChange:i=>W(i.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-all duration-300 ease-out text-sm hover:border-army-400 hover:shadow-md cursor-pointer",children:[e.jsx("option",{value:"",children:"Choose..."}),g.map(i=>e.jsx("option",{value:i,children:i},i))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Flavor #2"}),e.jsxs("select",{value:j,onChange:i=>me(i.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200 text-sm",children:[e.jsx("option",{value:"",children:"Choose..."}),g.map(i=>e.jsx("option",{value:i,children:i},i))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Flavor #3"}),e.jsxs("select",{value:M,onChange:i=>he(i.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200 text-sm",children:[e.jsx("option",{value:"",children:"Choose..."}),g.map(i=>e.jsx("option",{value:i,children:i},i))]})]})]}),b&&j&&M&&e.jsx("div",{className:"bg-army-50 border border-army-200 rounded-lg p-3",children:e.jsxs("p",{className:"text-sm font-medium text-army-800",children:["Your Bundle: ",b," + ",j," + ",M]})})]})}if(y){const c=s.find(i=>i.name==="Box Selection"),g=s.find(i=>i.name==="Type");return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Blend Box Selection"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Choose your blend combination"})]}),c&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Blend Combination"}),e.jsxs("select",{value:m,onChange:i=>T(i.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose combination..."}),c.optionValues.map(i=>e.jsx("option",{value:i.name,children:i.name},i.name))]})]}),g&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Grind Type"}),e.jsxs("select",{value:x,onChange:i=>F(i.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose grind type..."}),g.optionValues.map(i=>e.jsx("option",{value:i.name,children:i.name},i.name))]})]})]})}if(C){const c=s.find(g=>g.name==="Type"||g.name==="Grind"||g.name==="Grind Type"||g.name.toLowerCase().includes("type")||g.name.toLowerCase().includes("grind"));return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Roasters Box Selection"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Select your grind preference"})]}),c?e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:c.name}),e.jsxs("select",{value:x,onChange:g=>F(g.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsxs("option",{value:"",children:["Choose ",c.name.toLowerCase(),"..."]}),c.optionValues.map(g=>e.jsx("option",{value:g.name,children:g.name},g.name))]})]}):e.jsx("div",{className:"text-gray-500 text-sm",children:"No variant options available for this product."})]})}const $=s.find(c=>c.name==="Size"),N=s.find(c=>c.name==="type"||c.name==="Type");return e.jsxs(e.Fragment,{children:[$&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Size"}),e.jsxs("select",{value:m,onChange:c=>T(c.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsx("option",{value:"",children:"Choose size..."}),$.optionValues.map(c=>e.jsx("option",{value:c.name,children:c.name},c.name))]})]}),N&&N.optionValues&&N.optionValues.length>0&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:N.name==="type"?"Type":N.name}),e.jsxs("select",{value:x,onChange:c=>F(c.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 transition-colors duration-200",children:[e.jsxs("option",{value:"",children:["Choose ",N.name.toLowerCase(),"..."]}),N.optionValues.map(c=>e.jsx("option",{value:c.name,children:c.name},c.name))]})]})]})})()}):(D=u.data)!=null&&D.error?e.jsxs("div",{className:"text-center py-4 text-red-500",children:["Error loading product options: ",u.data.error]}):e.jsx("div",{className:"text-center py-4 text-gray-500",children:"Select a coffee product to see available options"})}),o==="kcups"&&ne&&e.jsxs("div",{className:"mb-6 transition-all duration-200",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Select Flavor"}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:(O=(q=ne.variants)==null?void 0:q.nodes)==null?void 0:O.map(s=>{var f,y;return e.jsx("button",{onClick:()=>h(s.id),className:`px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-300 ease-out text-left transform hover:scale-105 hover:-translate-y-0.5 ${R===s.id?"border-army-600 bg-army-600 text-white shadow-md hover:bg-army-700 hover:shadow-lg":"border-gray-300 bg-white text-gray-700 hover:border-army-500 hover:text-army-600 hover:bg-army-50 hover:shadow-md"}`,children:((y=(f=s.selectedOptions)==null?void 0:f.find(C=>C.name.toLowerCase().includes("title")||C.name.toLowerCase().includes("flavor")))==null?void 0:y.value)||s.title||"K-Cup Variant"},s.id)})})]}),o&&(o==="coffee"&&p&&(m&&x||b&&j&&M||((I=u.data)==null?void 0:I.product)&&(u.data.product.id==="gid://shopify/Product/10111587647803"||u.data.product.title.toLowerCase().includes("roasters box"))&&x)||o==="kcups"&&R)&&e.jsxs("div",{className:"mb-6 transition-all duration-200",children:[e.jsx("label",{className:"text-lg font-semibold text-gray-900 mb-3 block",children:"Delivery Frequency"}),e.jsxs("div",{className:"relative",ref:ge,children:[e.jsx("button",{type:"button",onClick:()=>fe(!xe),className:"w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 hover:border-gray-400 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium text-gray-900",children:_e.label}),e.jsx("svg",{className:`w-5 h-5 text-gray-400 transition-transform duration-200 ${xe?"transform rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),xe&&e.jsx("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg",children:ie.map(s=>e.jsx("button",{type:"button",onClick:()=>{We(s.value),fe(!1)},className:`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 first:rounded-t-lg last:rounded-b-lg ${oe===s.value?"bg-army-50 border-l-4 border-army-500":""}`,children:e.jsx("span",{className:`font-medium ${oe===s.value?"text-army-900":"text-gray-900"}`,children:s.label})},s.value))})]})]})]}),o&&(o==="coffee"&&p&&(m&&x||b&&j&&M||((S=u.data)==null?void 0:S.product)&&(u.data.product.id==="gid://shopify/Product/10111587647803"||u.data.product.title.toLowerCase().includes("roasters box"))&&x)||o==="kcups"&&R)&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("span",{className:"text-lg font-semibold text-gray-900",children:"Quantity:"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:()=>Pe(Math.max(1,ae-1)),className:"w-10 h-10 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4"})})}),e.jsx("span",{className:"text-xl font-semibold text-gray-900 min-w-[3rem] text-center",children:ae}),e.jsx("button",{onClick:()=>Pe(ae+1),className:"w-10 h-10 rounded-lg bg-white border border-gray-300 flex items-center justify-center hover:border-army-400 transition-colors",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})})]})]}),e.jsx("div",{className:"space-y-3",children:(()=>{var y,C,$,N,H,c,g,i,Be,Te,Re,He,Oe;const s=ie.find(V=>V.value===oe);let f=null;if(o==="coffee"&&p&&((y=u.data)!=null&&y.product)){const V=u.data.product.id==="gid://shopify/Product/8965076156731"||Le(u.data.product).some(B=>B.name.includes("Flavor #")),pe=u.data.product.id==="gid://shopify/Product/10111589941563",be=u.data.product.id==="gid://shopify/Product/10111587647803";V&&b&&j&&M?f=($=(C=u.data.product.variants)==null?void 0:C.nodes)==null?void 0:$.find(B=>{var Y,A,$e;const z=B.selectedOptions||[],le=((Y=z.find(_=>_.name==="Flavor #1"))==null?void 0:Y.value)===b,P=((A=z.find(_=>_.name==="Flavor #2"))==null?void 0:A.value)===j,ce=(($e=z.find(_=>_.name==="Flavor #3"))==null?void 0:$e.value)===M;return le&&P&&ce}):pe&&m&&x?f=(H=(N=u.data.product.variants)==null?void 0:N.nodes)==null?void 0:H.find(B=>{var ce,Y;const z=B.selectedOptions||[],le=((ce=z.find(A=>A.name==="Box Selection"))==null?void 0:ce.value)===m,P=((Y=z.find(A=>A.name==="Type"))==null?void 0:Y.value)===x;return le&&P}):be&&x?f=(g=(c=u.data.product.variants)==null?void 0:c.nodes)==null?void 0:g.find(B=>(B.selectedOptions||[]).find(P=>(P.name==="Type"||P.name==="Grind"||P.name==="Grind Type"||P.name.toLowerCase().includes("type")||P.name.toLowerCase().includes("grind"))&&P.value===x)):m&&x&&(f=Ye(u.data.product,{Size:m,type:x}))}else o==="kcups"&&R&&ne&&(f=(Be=(i=ne.variants)==null?void 0:i.nodes)==null?void 0:Be.find(V=>V.id===R));if(f&&s)return e.jsx(Ve,{onClick:()=>Ae("cart"),lines:[{merchandiseId:f.id,quantity:ae,sellingPlanId:s.sellingPlanId}],className:`w-full py-4 px-6 rounded-lg font-semibold text-lg transition-colors duration-200 text-white ${o==="coffee"?"bg-army-600 hover:bg-army-700":"bg-amber-600 hover:bg-amber-700"}`,children:"Start Subscription"});if(o==="coffee"&&p&&s&&!f){const V=m&&x,pe=b&&j&&M,be=m&&x,B=((Te=u.data)==null?void 0:Te.product)&&(u.data.product.id==="gid://shopify/Product/10111587647803"||u.data.product.title.toLowerCase().includes("roasters box"))&&x;if(V||pe||be||B)return e.jsxs("div",{className:"text-center py-4 text-red-500 text-sm",children:["Unable to find matching variant. Please try different selections.",e.jsx("br",{}),e.jsxs("span",{className:"text-xs text-gray-500",children:["Product: ",((He=(Re=u.data)==null?void 0:Re.product)==null?void 0:He.title)||"Unknown"," | Selections: Size=",m,", Type=",x,", Flavors=",b,",",j,",",M]})]})}return o==="coffee"&&p?e.jsxs("div",{className:"text-center py-4 text-gray-500 text-xs",children:["Debug: Product selected but no button.",e.jsx("br",{}),"Product: ",G==null?void 0:G.title,e.jsx("br",{}),"Fetcher data: ",(Oe=u.data)!=null&&Oe.product?"Yes":"No",e.jsx("br",{}),"Selections: Size=",m,", Type=",x,e.jsx("br",{}),"Frequency: ",s==null?void 0:s.label,e.jsx("br",{}),"Variant found: ",f?"Yes":"No"]}):null})()})]}),e.jsx("div",{className:"mt-6",children:e.jsx("a",{href:"/pages/subscriptions",className:"block w-full text-center bg-army-600 hover:bg-army-700 py-3 px-6 rounded-lg font-medium transition-colors duration-200",style:{color:"white"},children:e.jsx("span",{className:"text-white",children:"Learn More About Subscriptions"})})})]})]})})]},t)}return e.jsxs("div",{ref:t==="coffee"?ee:re,className:"mb-20",id:`section-${t}`,style:{scrollMarginTop:"188px"},children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-12 h-12 bg-army-100 rounded-xl flex items-center justify-center mr-4 text-army-600",children:a.icon}),e.jsxs("div",{children:[e.jsx("h2",{className:`text-4xl font-bold transition-colors duration-200 ${w===t?"text-amber-600":"text-gray-900"}`,style:{fontFamily:"var(--font-title)"},children:a.title}),e.jsx("p",{className:"text-lg text-gray-600 mt-1",children:a.description})]})]}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsx("span",{className:`px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200 ${w===t?"bg-amber-100 text-amber-800":"bg-army-100 text-army-800"}`,children:t==="subscriptions"?"1 service":`${n.length} ${n.length===1?"item":"items"}`})})]}),e.jsx("div",{className:`h-1 rounded-full transition-all duration-300 ${w===t?"bg-amber-600 w-24":"bg-gray-200 w-16"}`})]}),((n==null?void 0:n.length)||0)>0?e.jsx("div",{className:`grid gap-6 ${Q==="grid"?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:n.map((o,v)=>{const p=o.id==="gid://shopify/Product/10111589941563"||o.id==="gid://shopify/Product/10111587647803"?{...o,availableForSale:!0}:o;return e.jsx(et,{product:p,loading:v<6?"eager":"lazy",viewMode:Q},o.id)})}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6 text-gray-400",children:e.jsx("div",{className:"w-12 h-12",children:a.icon})}),e.jsxs("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:["No ",a.title.toLowerCase()," found"]}),e.jsx("p",{className:"text-gray-600 mb-6",children:((l==null?void 0:l.length)||0)===0?`We don't have any ${a.title.toLowerCase()} products yet.`:"Try adjusting your filters to see more results."}),((l==null?void 0:l.length)||0)>0&&e.jsx("button",{onClick:()=>{const o=new URLSearchParams,v=k.get("section");v&&o.set("section",v),ze(`?${o.toString()}`)},className:"btn-primary",children:"Clear All Filters"})]})]},t)})})}),e.jsx("div",{className:"bg-army-600 py-20",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center text-white",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("span",{className:"inline-flex items-center text-amber-300 text-sm font-medium mb-4",children:[e.jsx("img",{src:"/coffeeicon.svg",alt:"Coffee",className:"w-5 h-5 mr-2 filter brightness-0 invert"}),"Our Origin Story"]}),e.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",style:{fontFamily:"var(--font-title)"},children:"Why Nicaragua?"}),e.jsx("div",{className:"w-24 h-1 bg-amber-500 rounded mx-auto mb-8"})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-8 text-left",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold mb-4 text-amber-300",children:"Perfect Growing Conditions"}),e.jsx("p",{className:"text-lg leading-relaxed opacity-90",children:"Nicaragua's volcanic soil, high altitude regions, and tropical climate create ideal conditions for coffee cultivation. The mountainous regions provide perfect elevation and temperature variations that allow coffee cherries to develop slowly, concentrating their flavors."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold mb-4 text-amber-300",children:"Exceptional Flavor Profile"}),e.jsx("p",{className:"text-lg leading-relaxed opacity-90",children:"Nicaraguan coffee is renowned for its well-balanced flavor profile, featuring bright acidity, medium to full body, and complex flavor notes ranging from chocolate and nuts to fruity and floral undertones - perfect for any brewing method."})]})]}),e.jsx("div",{className:"mt-12 p-8 bg-army-700 rounded-xl",children:e.jsx("p",{className:"text-xl leading-relaxed",children:e.jsx("strong",{children:"Every cup of Big River Coffee tells the story of Nicaragua's rich coffee heritage, sustainable farming practices, and the passionate farmers who make it all possible."})})})]})})})]})]})," "]})});export{Ft as default,Lt as meta};
