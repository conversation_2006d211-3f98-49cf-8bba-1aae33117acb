import{j as c}from"./jsx-runtime-D5QEUsP9.js";import{r}from"./chunk-D4RADZKF-DNKU_WG6.js";function q({src:w,fallbackImage:v,poster:E,className:p="",style:j={},autoPlay:o=!0,muted:y=!0,loop:I=!0,playsInline:V=!0,preload:O="metadata",lazy:h=!0,onLoadStart:a,onCanPlay:u,onError:d}){const t=r.useRef(null),[m,R]=r.useState(!h),[n,$]=r.useState(!1),[F,H]=r.useState(!1),[x,l]=r.useState(!1);if(r.useEffect(()=>{if(!h||m)return;const e=new IntersectionObserver(s=>{s.forEach(i=>{i.isIntersecting&&(R(!0),e.disconnect())})},{rootMargin:"50px",threshold:.1});return t.current&&e.observe(t.current),()=>e.disconnect()},[h,m]),r.useEffect(()=>{const e=t.current;if(!e)return;const s=()=>{a==null||a()},i=()=>{$(!0),u==null||u()},g=()=>{H(!0),d==null||d()},b=()=>{l(!0)},L=()=>{l(!1)};return e.addEventListener("loadstart",s),e.addEventListener("canplay",i),e.addEventListener("error",g),e.addEventListener("play",b),e.addEventListener("pause",L),()=>{e.removeEventListener("loadstart",s),e.removeEventListener("canplay",i),e.removeEventListener("error",g),e.removeEventListener("play",b),e.removeEventListener("pause",L)}},[a,u,d]),r.useEffect(()=>{if(n&&o&&t.current&&!x){const e=t.current.play();e!==void 0&&e.catch(s=>{console.warn("Video autoplay failed:",s)})}},[n,o,x]),r.useEffect(()=>{if(!t.current)return;const e=new IntersectionObserver(s=>{s.forEach(i=>{t.current&&(i.isIntersecting?o&&n&&(t.current.play().catch(()=>{}),l(!0)):(t.current.pause(),l(!1)))})},{threshold:.3,rootMargin:"50px"});return e.observe(t.current),()=>e.disconnect()},[o,n]),F&&v)return c.jsx("img",{src:v,alt:"Video fallback",className:p,style:j});const f=E||v;return c.jsxs("div",{className:"relative",style:j,children:[!n&&f&&c.jsx("img",{src:f,alt:"Loading...",className:`${p} absolute inset-0 w-full h-full object-cover`,style:{objectFit:"cover"},loading:"eager"}),m&&c.jsxs("video",{ref:t,className:`${p} ${n?"opacity-100":"opacity-0"} transition-opacity duration-300`,autoPlay:o,muted:y,loop:I,playsInline:V,preload:O,poster:E,style:{objectFit:"cover"},children:[c.jsx("source",{src:w,type:"video/mp4"}),f&&c.jsx("img",{src:f,alt:"Video not supported",className:"w-full h-full object-cover"})]})]})}export{q as O};
