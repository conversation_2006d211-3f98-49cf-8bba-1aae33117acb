{"inputs": {"node_modules/@shopify/hydrogen-react/dist/node-prod/codegen.helpers.mjs": {"bytes": 531, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-queries.mjs": {"bytes": 5755, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/@xstate/fsm/es/index.mjs": {"bytes": 5654, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector2.mjs": {"bytes": 119, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector.production.min.mjs": {"bytes": 151, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector.development.mjs": {"bytes": 142, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/index.mjs": {"bytes": 94, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/use-sync-external-store-shim.production.min.mjs": {"bytes": 190, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/use-sync-external-store-shim.development.mjs": {"bytes": 181, "format": "esm", "imports": []}, "node_modules/@shopify/graphql-client/dist/index.mjs": {"bytes": 584, "format": "esm", "imports": [{"path": "node_modules/@shopify/graphql-client/dist/graphql-client/graphql-client.mjs", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/@shopify/graphql-client/dist/graphql-client/graphql-client.mjs"}, {"path": "node_modules/@shopify/graphql-client/dist/graphql-client/utilities.mjs", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/@shopify/graphql-client/dist/graphql-client/utilities.mjs"}, {"path": "node_modules/@shopify/graphql-client/dist/api-client-utilities/validations.mjs", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/@shopify/graphql-client/dist/api-client-utilities/validations.mjs"}, {"path": "node_modules/@shopify/graphql-client/dist/api-client-utilities/api-versions.mjs", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/@shopify/graphql-client/dist/api-client-utilities/api-versions.mjs"}, {"path": "node_modules/@shopify/graphql-client/dist/graphql-client/http-fetch.mjs", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/@shopify/graphql-client/dist/graphql-client/http-fetch.mjs"}, {"path": "node_modules/@shopify/graphql-client/dist/api-client-utilities/utilities.mjs", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/@shopify/graphql-client/dist/api-client-utilities/utilities.mjs"}]}, "node_modules/@shopify/admin-api-client/dist/index.mjs": {"bytes": 158, "format": "esm", "imports": [{"path": "node_modules/@shopify/admin-api-client/dist/graphql/client.mjs", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/@shopify/admin-api-client/dist/graphql/client.mjs"}, {"path": "node_modules/@shopify/admin-api-client/dist/rest/client.mjs", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/@shopify/admin-api-client/dist/rest/client.mjs"}]}, "node_modules/react-router/dist/development/index.mjs": {"bytes": 5492, "format": "esm", "imports": [{"path": "node_modules/react-router/dist/development/chunk-D4RADZKF.mjs", "kind": "import-statement", "original": "./chunk-D4RADZKF.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/parse-metafield.mjs": {"bytes": 3483, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/flatten-connection.mjs", "kind": "import-statement", "original": "./flatten-connection.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector.mjs": {"bytes": 246, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/shim/with-selector.mjs", "kind": "import-statement", "original": "../node_modules/use-sync-external-store/shim/with-selector.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.mjs": {"bytes": 220, "format": "cjs", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/@xstate/react/es/useConstant.mjs": {"bytes": 248, "format": "cjs", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineQuantity.mjs": {"bytes": 417, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineProvider.mjs", "kind": "import-statement", "original": "./CartLineProvider.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.mjs": {"bytes": 2034, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/use-sync-external-store-shim.production.min.mjs", "kind": "import-statement", "original": "../../../_virtual/use-sync-external-store-shim.production.min.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.mjs": {"bytes": 5443, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/use-sync-external-store-shim.development.mjs", "kind": "import-statement", "original": "../../../_virtual/use-sync-external-store-shim.development.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineProvider.mjs": {"bytes": 578, "format": "cjs", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ExternalVideo.mjs": {"bytes": 1234, "format": "cjs", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/BaseButton.mjs": {"bytes": 841, "format": "cjs", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/RichText.components.mjs": {"bytes": 1390, "format": "cjs", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/shim/with-selector.mjs": {"bytes": 736, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector2.mjs", "kind": "import-statement", "original": "../../../_virtual/with-selector2.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.min.mjs", "kind": "import-statement", "original": "../cjs/use-sync-external-store-shim/with-selector.production.min.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.mjs", "kind": "import-statement", "original": "../cjs/use-sync-external-store-shim/with-selector.development.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/shim/index.mjs": {"bytes": 667, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/index.mjs", "kind": "import-statement", "original": "../../../_virtual/index.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.mjs", "kind": "import-statement", "original": "../cjs/use-sync-external-store-shim.production.min.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.mjs", "kind": "import-statement", "original": "../cjs/use-sync-external-store-shim.development.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartCost.mjs": {"bytes": 841, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/Money.mjs", "kind": "import-statement", "original": "./Money.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartProvider.mjs", "kind": "import-statement", "original": "./CartProvider.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ProductPrice.mjs": {"bytes": 2259, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/Money.mjs", "kind": "import-statement", "original": "./Money.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/flatten-connection.mjs", "kind": "import-statement", "original": "./flatten-connection.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/useCartActions.mjs": {"bytes": 4436, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-queries.mjs", "kind": "import-statement", "original": "./cart-queries.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-hooks.mjs", "kind": "import-statement", "original": "./cart-hooks.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.min.mjs": {"bytes": 2211, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector.production.min.mjs", "kind": "import-statement", "original": "../../../../_virtual/with-selector.production.min.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/shim/index.mjs", "kind": "import-statement", "original": "../../shim/index.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.mjs": {"bytes": 4427, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector.development.mjs", "kind": "import-statement", "original": "../../../../_virtual/with-selector.development.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/shim/index.mjs", "kind": "import-statement", "original": "../../shim/index.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ModelViewer.mjs": {"bytes": 5275, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/load-script.mjs", "kind": "import-statement", "original": "./load-script.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ProductProvider.mjs": {"bytes": 6694, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/flatten-connection.mjs", "kind": "import-statement", "original": "./flatten-connection.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/RichText.mjs": {"bytes": 3979, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/RichText.components.mjs", "kind": "import-statement", "original": "./RichText.components.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/Video.mjs": {"bytes": 1402, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/Image.mjs", "kind": "import-statement", "original": "./Image.mjs"}]}, "node_modules/@shopify/admin-api-client/dist/rest/client.mjs": {"bytes": 5230, "format": "esm", "imports": [{"path": "node_modules/@shopify/graphql-client/dist/index.mjs", "kind": "import-statement", "original": "@shopify/graphql-client"}, {"path": "node_modules/@shopify/admin-api-client/dist/validations.mjs", "kind": "import-statement", "original": "../validations.mjs"}, {"path": "node_modules/@shopify/admin-api-client/dist/constants.mjs", "kind": "import-statement", "original": "../constants.mjs"}, {"path": "node_modules/@shopify/admin-api-client/dist/rest/types.mjs", "kind": "import-statement", "original": "./types.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ShopPayButton.mjs": {"bytes": 3092, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ShopifyProvider.mjs", "kind": "import-statement", "original": "./ShopifyProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/load-script.mjs", "kind": "import-statement", "original": "./load-script.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-utils.mjs", "kind": "import-statement", "original": "./analytics-utils.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-hooks.mjs": {"bytes": 1508, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ShopifyProvider.mjs", "kind": "import-statement", "original": "./ShopifyProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-constants.mjs", "kind": "import-statement", "original": "./cart-constants.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cookies-utils.mjs", "kind": "import-statement", "original": "./cookies-utils.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/BuyNowButton.mjs": {"bytes": 1190, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartProvider.mjs", "kind": "import-statement", "original": "./CartProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/BaseButton.mjs", "kind": "import-statement", "original": "./BaseButton.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartCheckoutButton.mjs": {"bytes": 863, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartProvider.mjs", "kind": "import-statement", "original": "./CartProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/BaseButton.mjs", "kind": "import-statement", "original": "./BaseButton.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/MediaFile.mjs": {"bytes": 2029, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/Image.mjs", "kind": "import-statement", "original": "./Image.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/Video.mjs", "kind": "import-statement", "original": "./Video.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ExternalVideo.mjs", "kind": "import-statement", "original": "./ExternalVideo.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ModelViewer.mjs", "kind": "import-statement", "original": "./ModelViewer.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/useCartAPIStateMachine.mjs": {"bytes": 12497, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/@xstate/react/es/fsm.mjs", "kind": "import-statement", "original": "./node_modules/@xstate/react/es/fsm.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/@xstate/fsm/es/index.mjs", "kind": "import-statement", "original": "./node_modules/@xstate/fsm/es/index.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/flatten-connection.mjs", "kind": "import-statement", "original": "./flatten-connection.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/useCartActions.mjs", "kind": "import-statement", "original": "./useCartActions.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/@xstate/react/es/fsm.mjs": {"bytes": 2934, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/@xstate/fsm/es/index.mjs", "kind": "import-statement", "original": "../../fsm/es/index.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.mjs", "kind": "import-statement", "original": "../../../use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector.mjs", "kind": "import-statement", "original": "../../../../_virtual/with-selector.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/@xstate/react/es/useConstant.mjs", "kind": "import-statement", "original": "./useConstant.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/AddToCartButton.mjs": {"bytes": 2162, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartProvider.mjs", "kind": "import-statement", "original": "./CartProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ProductProvider.mjs", "kind": "import-statement", "original": "./ProductProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/BaseButton.mjs", "kind": "import-statement", "original": "./BaseButton.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineQuantityAdjustButton.mjs": {"bytes": 1693, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartProvider.mjs", "kind": "import-statement", "original": "./CartProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineProvider.mjs", "kind": "import-statement", "original": "./CartLineProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/BaseButton.mjs", "kind": "import-statement", "original": "./BaseButton.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartProvider.mjs": {"bytes": 14176, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/useCartAPIStateMachine.mjs", "kind": "import-statement", "original": "./useCartAPIStateMachine.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-constants.mjs", "kind": "import-statement", "original": "./cart-constants.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-queries.mjs", "kind": "import-statement", "original": "./cart-queries.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ShopifyProvider.mjs", "kind": "import-statement", "original": "./ShopifyProvider.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/index.mjs": {"bytes": 3325, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/AddToCartButton.mjs", "kind": "import-statement", "original": "./AddToCartButton.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics.mjs", "kind": "import-statement", "original": "./analytics.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-constants.mjs", "kind": "import-statement", "original": "./analytics-constants.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-utils.mjs", "kind": "import-statement", "original": "./analytics-utils.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/BuyNowButton.mjs", "kind": "import-statement", "original": "./BuyNowButton.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-constants.mjs", "kind": "import-statement", "original": "./cart-constants.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartCheckoutButton.mjs", "kind": "import-statement", "original": "./CartCheckoutButton.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartCost.mjs", "kind": "import-statement", "original": "./CartCost.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineProvider.mjs", "kind": "import-statement", "original": "./CartLineProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineQuantity.mjs", "kind": "import-statement", "original": "./CartLineQuantity.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineQuantityAdjustButton.mjs", "kind": "import-statement", "original": "./CartLineQuantityAdjustButton.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/CartProvider.mjs", "kind": "import-statement", "original": "./CartProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/codegen.helpers.mjs", "kind": "import-statement", "original": "./codegen.helpers.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cookies-utils.mjs", "kind": "import-statement", "original": "./cookies-utils.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ExternalVideo.mjs", "kind": "import-statement", "original": "./ExternalVideo.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/flatten-connection.mjs", "kind": "import-statement", "original": "./flatten-connection.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/getProductOptions.mjs", "kind": "import-statement", "original": "./getProductOptions.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/Image.mjs", "kind": "import-statement", "original": "./Image.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/load-script.mjs", "kind": "import-statement", "original": "./load-script.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/MediaFile.mjs", "kind": "import-statement", "original": "./MediaFile.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ModelViewer.mjs", "kind": "import-statement", "original": "./ModelViewer.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/Money.mjs", "kind": "import-statement", "original": "./Money.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/optionValueDecoder.mjs", "kind": "import-statement", "original": "./optionValueDecoder.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/parse-metafield.mjs", "kind": "import-statement", "original": "./parse-metafield.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ProductPrice.mjs", "kind": "import-statement", "original": "./ProductPrice.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ProductProvider.mjs", "kind": "import-statement", "original": "./ProductProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/RichText.mjs", "kind": "import-statement", "original": "./RichText.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ShopifyProvider.mjs", "kind": "import-statement", "original": "./ShopifyProvider.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ShopPayButton.mjs", "kind": "import-statement", "original": "./ShopPayButton.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/storefront-client.mjs", "kind": "import-statement", "original": "./storefront-client.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/useMoney.mjs", "kind": "import-statement", "original": "./useMoney.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/useSelectedOptionInUrlParam.mjs", "kind": "import-statement", "original": "./useSelectedOptionInUrlParam.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/useShopifyCookies.mjs", "kind": "import-statement", "original": "./useShopifyCookies.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/Video.mjs", "kind": "import-statement", "original": "./Video.mjs"}]}, "node_modules/react/cjs/react.production.min.js": {"bytes": 6930, "format": "cjs", "imports": []}, "app/assets/favicon.svg": {"bytes": 41, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-constants.mjs": {"bytes": 1185, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/optionValueDecoder.mjs": {"bytes": 3297, "format": "esm", "imports": []}, "node_modules/worktop/cookie/index.mjs": {"bytes": 1234, "format": "esm", "imports": []}, "node_modules/@shopify/admin-api-client/dist/rest/types.mjs": {"bytes": 233, "format": "esm", "imports": []}, "node_modules/@shopify/graphql-client/dist/api-client-utilities/validations.mjs": {"bytes": 1653, "format": "esm", "imports": []}, "node_modules/@shopify/graphql-client/dist/api-client-utilities/utilities.mjs": {"bytes": 859, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-constants.mjs": {"bytes": 489, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen/dist/production/log-seo-tags-TY72EQWZ.js": {"bytes": 1470, "format": "esm", "imports": []}, "node_modules/content-security-policy-builder/esm/mod.js": {"bytes": 854, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/storefront-api-constants.mjs": {"bytes": 115, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/packages/hydrogen-react/package.json.mjs": {"bytes": 92, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-utils.mjs": {"bytes": 1608, "format": "esm", "imports": []}, "node_modules/@shopify/admin-api-client/dist/constants.mjs": {"bytes": 522, "format": "esm", "imports": []}, "node_modules/isbot/index.mjs": {"bytes": 6329, "format": "esm", "imports": []}, "node_modules/cookie/dist/index.js": {"bytes": 8340, "format": "cjs", "imports": []}, "node_modules/scheduler/cjs/scheduler.production.min.js": {"bytes": 4235, "format": "cjs", "imports": []}, "node_modules/react/cjs/react-jsx-runtime.production.min.js": {"bytes": 859, "format": "cjs", "imports": [{"path": "node_modules/react/index.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/react/index.js"}]}, "node_modules/react/index.js": {"bytes": 190, "format": "cjs", "imports": [{"path": "node_modules/react/cjs/react.production.min.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/react/cjs/react.production.min.js"}]}, "node_modules/react-dom/index.js": {"bytes": 1363, "format": "cjs", "imports": [{"path": "node_modules/react-dom/cjs/react-dom.production.min.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/react-dom/cjs/react-dom.production.min.js"}]}, "node_modules/react/jsx-runtime.js": {"bytes": 214, "format": "cjs", "imports": [{"path": "node_modules/react/cjs/react-jsx-runtime.production.min.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/react/cjs/react-jsx-runtime.production.min.js"}]}, "node_modules/react-dom/server.browser.js": {"bytes": 658, "format": "cjs", "imports": [{"path": "node_modules/react-dom/cjs/react-dom-server-legacy.browser.production.min.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/react-dom/cjs/react-dom-server-legacy.browser.production.min.js"}, {"path": "node_modules/react-dom/cjs/react-dom-server.browser.production.min.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/react-dom/cjs/react-dom-server.browser.production.min.js"}]}, "app/styles/tailwind.css?url": {"bytes": 303, "format": "esm", "imports": []}, "app/styles/homepage.css?url": {"bytes": 303, "format": "esm", "imports": []}, "app/styles/app.css?url": {"bytes": 288, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/getProductOptions.mjs": {"bytes": 8651, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/optionValueDecoder.mjs", "kind": "import-statement", "original": "./optionValueDecoder.mjs"}]}, "app/styles/reset.css?url": {"bytes": 294, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen-react/dist/node-prod/storefront-client.mjs": {"bytes": 4008, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/storefront-api-constants.mjs", "kind": "import-statement", "original": "./storefront-api-constants.mjs"}]}, "node_modules/@shopify/graphql-client/dist/graphql-client/utilities.mjs": {"bytes": 2268, "format": "esm", "imports": [{"path": "node_modules/@shopify/graphql-client/dist/graphql-client/constants.mjs", "kind": "import-statement", "original": "./constants.mjs"}]}, "node_modules/@shopify/admin-api-client/dist/validations.mjs": {"bytes": 508, "format": "esm", "imports": [{"path": "node_modules/@shopify/admin-api-client/dist/constants.mjs", "kind": "import-statement", "original": "./constants.mjs"}]}, "node_modules/@shopify/remix-oxygen/dist/production/index.js": {"bytes": 1090, "format": "esm", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/load-script.mjs": {"bytes": 1479, "format": "cjs", "imports": []}, "node_modules/@shopify/graphql-client/dist/graphql-client/http-fetch.mjs": {"bytes": 2456, "format": "esm", "imports": [{"path": "node_modules/@shopify/graphql-client/dist/graphql-client/constants.mjs", "kind": "import-statement", "original": "./constants.mjs"}, {"path": "node_modules/@shopify/graphql-client/dist/graphql-client/utilities.mjs", "kind": "import-statement", "original": "./utilities.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/cookies-utils.mjs": {"bytes": 1392, "format": "esm", "imports": [{"path": "node_modules/worktop/cookie/index.mjs", "kind": "import-statement", "original": "worktop/cookie"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-constants.mjs", "kind": "import-statement", "original": "./cart-constants.mjs"}]}, "\u0000virtual:react-router/with-props": {"bytes": 957, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/useSelectedOptionInUrlParam.mjs": {"bytes": 1069, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/getProductOptions.mjs", "kind": "import-statement", "original": "./getProductOptions.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/Money.mjs": {"bytes": 1310, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/useMoney.mjs", "kind": "import-statement", "original": "./useMoney.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/useMoney.mjs": {"bytes": 4087, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/ShopifyProvider.mjs", "kind": "import-statement", "original": "./ShopifyProvider.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-schema-trekkie-storefront-page-view.mjs": {"bytes": 1902, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-constants.mjs", "kind": "import-statement", "original": "./analytics-constants.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-utils.mjs", "kind": "import-statement", "original": "./analytics-utils.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cookies-utils.mjs", "kind": "import-statement", "original": "./cookies-utils.mjs"}]}, "node_modules/@shopify/graphql-client/dist/graphql-client/graphql-client.mjs": {"bytes": 13020, "format": "esm", "imports": [{"path": "node_modules/@shopify/graphql-client/dist/graphql-client/http-fetch.mjs", "kind": "import-statement", "original": "./http-fetch.mjs"}, {"path": "node_modules/@shopify/graphql-client/dist/graphql-client/constants.mjs", "kind": "import-statement", "original": "./constants.mjs"}, {"path": "node_modules/@shopify/graphql-client/dist/graphql-client/utilities.mjs", "kind": "import-statement", "original": "./utilities.mjs"}]}, "node_modules/@shopify/admin-api-client/dist/graphql/client.mjs": {"bytes": 3049, "format": "esm", "imports": [{"path": "node_modules/@shopify/graphql-client/dist/index.mjs", "kind": "import-statement", "original": "@shopify/graphql-client"}, {"path": "node_modules/@shopify/admin-api-client/dist/constants.mjs", "kind": "import-statement", "original": "../constants.mjs"}, {"path": "node_modules/@shopify/admin-api-client/dist/validations.mjs", "kind": "import-statement", "original": "../validations.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-schema-custom-storefront-customer-tracking.mjs": {"bytes": 7049, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-constants.mjs", "kind": "import-statement", "original": "./analytics-constants.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-utils.mjs", "kind": "import-statement", "original": "./analytics-utils.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cookies-utils.mjs", "kind": "import-statement", "original": "./cookies-utils.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/packages/hydrogen-react/package.json.mjs", "kind": "import-statement", "original": "./packages/hydrogen-react/package.json.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/useShopifyCookies.mjs": {"bytes": 1916, "format": "cjs", "imports": [{"path": "node_modules/worktop/cookie/index.mjs", "kind": "import-statement", "original": "worktop/cookie"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-constants.mjs", "kind": "import-statement", "original": "./cart-constants.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cookies-utils.mjs", "kind": "import-statement", "original": "./cookies-utils.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ShopifyProvider.mjs": {"bytes": 2798, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/storefront-api-constants.mjs", "kind": "import-statement", "original": "./storefront-api-constants.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/storefront-client.mjs", "kind": "import-statement", "original": "./storefront-client.mjs"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics.mjs": {"bytes": 5459, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-constants.mjs", "kind": "import-statement", "original": "./cart-constants.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-constants.mjs", "kind": "import-statement", "original": "./analytics-constants.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-utils.mjs", "kind": "import-statement", "original": "./analytics-utils.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/cookies-utils.mjs", "kind": "import-statement", "original": "./cookies-utils.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-schema-trekkie-storefront-page-view.mjs", "kind": "import-statement", "original": "./analytics-schema-trekkie-storefront-page-view.mjs"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-schema-custom-storefront-customer-tracking.mjs", "kind": "import-statement", "original": "./analytics-schema-custom-storefront-customer-tracking.mjs"}]}, "\u0000virtual:react-router/server-build": {"bytes": 14245, "format": "esm", "imports": [{"path": "app/entry.server.tsx", "kind": "import-statement", "original": "/app/entry.server.tsx"}, {"path": "app/root.tsx", "kind": "import-statement", "original": "/app/root.tsx"}, {"path": "app/routes/[robots.txt].tsx", "kind": "import-statement", "original": "/app/routes/[robots.txt].tsx"}, {"path": "app/routes/($locale).tsx", "kind": "import-statement", "original": "/app/routes/($locale).tsx"}, {"path": "app/routes/($locale).blogs.$blogHandle.$articleHandle.tsx", "kind": "import-statement", "original": "/app/routes/($locale).blogs.$blogHandle.$articleHandle.tsx"}, {"path": "app/routes/($locale).api.$version.[graphql.json].tsx", "kind": "import-statement", "original": "/app/routes/($locale).api.$version.[graphql.json].tsx"}, {"path": "app/routes/($locale).sitemap.$type.$page[.xml].tsx", "kind": "import-statement", "original": "/app/routes/($locale).sitemap.$type.$page[.xml].tsx"}, {"path": "app/routes/($locale).blogs.$blogHandle._index.tsx", "kind": "import-statement", "original": "/app/routes/($locale).blogs.$blogHandle._index.tsx"}, {"path": "app/routes/($locale).collections.all-coffees.tsx", "kind": "import-statement", "original": "/app/routes/($locale).collections.all-coffees.tsx"}, {"path": "app/routes/($locale).products.roasters-box.tsx", "kind": "import-statement", "original": "/app/routes/($locale).products.roasters-box.tsx"}, {"path": "app/routes/($locale).collections.$handle.tsx", "kind": "import-statement", "original": "/app/routes/($locale).collections.$handle.tsx"}, {"path": "app/routes/($locale).pages.subscriptions.tsx", "kind": "import-statement", "original": "/app/routes/($locale).pages.subscriptions.tsx"}, {"path": "app/routes/($locale).account_.authorize.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account_.authorize.tsx"}, {"path": "app/routes/($locale).collections._index.tsx", "kind": "import-statement", "original": "/app/routes/($locale).collections._index.tsx"}, {"path": "app/routes/($locale).account_.register.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account_.register.tsx"}, {"path": "app/routes/($locale).policies.$handle.tsx", "kind": "import-statement", "original": "/app/routes/($locale).policies.$handle.tsx"}, {"path": "app/routes/($locale).products.$handle.tsx", "kind": "import-statement", "original": "/app/routes/($locale).products.$handle.tsx"}, {"path": "app/routes/($locale).account_.logout.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account_.logout.tsx"}, {"path": "app/routes/($locale).collections.all.tsx", "kind": "import-statement", "original": "/app/routes/($locale).collections.all.tsx"}, {"path": "app/routes/($locale).pages.nicaragua.tsx", "kind": "import-statement", "original": "/app/routes/($locale).pages.nicaragua.tsx"}, {"path": "app/routes/($locale).policies._index.tsx", "kind": "import-statement", "original": "/app/routes/($locale).policies._index.tsx"}, {"path": "app/routes/($locale).account_.login.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account_.login.tsx"}, {"path": "app/routes/($locale).discount.$code.tsx", "kind": "import-statement", "original": "/app/routes/($locale).discount.$code.tsx"}, {"path": "app/routes/($locale).pages.$handle.tsx", "kind": "import-statement", "original": "/app/routes/($locale).pages.$handle.tsx"}, {"path": "app/routes/($locale).[sitemap.xml].tsx", "kind": "import-statement", "original": "/app/routes/($locale).[sitemap.xml].tsx"}, {"path": "app/routes/($locale).blogs._index.tsx", "kind": "import-statement", "original": "/app/routes/($locale).blogs._index.tsx"}, {"path": "app/routes/($locale).pages.brew.tsx", "kind": "import-statement", "original": "/app/routes/($locale).pages.brew.tsx"}, {"path": "app/routes/($locale).affiliate.tsx", "kind": "import-statement", "original": "/app/routes/($locale).affiliate.tsx"}, {"path": "app/routes/($locale).our-story.tsx", "kind": "import-statement", "original": "/app/routes/($locale).our-story.tsx"}, {"path": "app/routes/($locale).account.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account.tsx"}, {"path": "app/routes/($locale).account.orders._index.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account.orders._index.tsx"}, {"path": "app/routes/($locale).account.orders.$id.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account.orders.$id.tsx"}, {"path": "app/routes/($locale).account.addresses.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account.addresses.tsx"}, {"path": "app/routes/($locale).account.profile.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account.profile.tsx"}, {"path": "app/routes/($locale).account._index.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account._index.tsx"}, {"path": "app/routes/($locale).account.$.tsx", "kind": "import-statement", "original": "/app/routes/($locale).account.$.tsx"}, {"path": "app/routes/($locale).contact.tsx", "kind": "import-statement", "original": "/app/routes/($locale).contact.tsx"}, {"path": "app/routes/($locale).search.tsx", "kind": "import-statement", "original": "/app/routes/($locale).search.tsx"}, {"path": "app/routes/($locale)._index.tsx", "kind": "import-statement", "original": "/app/routes/($locale)._index.tsx"}, {"path": "app/routes/($locale).cart.tsx", "kind": "import-statement", "original": "/app/routes/($locale).cart.tsx"}, {"path": "app/routes/($locale).cart.$lines.tsx", "kind": "import-statement", "original": "/app/routes/($locale).cart.$lines.tsx"}, {"path": "app/routes/($locale).$.tsx", "kind": "import-statement", "original": "/app/routes/($locale).$.tsx"}, {"path": "\u0000virtual:react-router/server-manifest", "kind": "import-statement", "original": "\u0000virtual:react-router/server-manifest"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/flatten-connection.mjs": {"bytes": 760, "format": "esm", "imports": []}, "node_modules/@shopify/graphql-client/dist/api-client-utilities/api-versions.mjs": {"bytes": 1254, "format": "esm", "imports": []}, "\u0000virtual:react-router/server-manifest": {"bytes": 38766, "format": "esm", "imports": []}, "node_modules/@shopify/graphql-client/dist/graphql-client/constants.mjs": {"bytes": 1414, "format": "esm", "imports": []}, "app/lib/fragments.ts": {"bytes": 3737, "format": "esm", "imports": []}, "node_modules/set-cookie-parser/lib/set-cookie.js": {"bytes": 6630, "format": "cjs", "imports": []}, "node_modules/scheduler/index.js": {"bytes": 198, "format": "cjs", "imports": [{"path": "node_modules/scheduler/cjs/scheduler.production.min.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/scheduler/cjs/scheduler.production.min.js"}]}, "node_modules/@shopify/hydrogen-react/dist/node-prod/Image.mjs": {"bytes": 10040, "format": "cjs", "imports": []}, "app/lib/utm.ts": {"bytes": 4559, "format": "esm", "imports": []}, "node_modules/react-dom/cjs/react-dom-server-legacy.browser.production.min.js": {"bytes": 35019, "format": "cjs", "imports": [{"path": "node_modules/react/index.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/react/index.js"}]}, "node_modules/react-dom/cjs/react-dom-server.browser.production.min.js": {"bytes": 35597, "format": "cjs", "imports": [{"path": "node_modules/react/index.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/react/index.js"}]}, "app/lib/scrollAnimations.ts": {"bytes": 3287, "format": "esm", "imports": []}, "app/lib/i18n.ts": {"bytes": 657, "format": "esm", "imports": []}, "app/lib/search.ts": {"bytes": 2179, "format": "esm", "imports": []}, "app/graphql/customer-account/CustomerOrderQuery.ts": {"bytes": 1479, "format": "esm", "imports": []}, "app/routes/($locale).$.tsx": {"bytes": 279, "format": "esm", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}]}, "app/graphql/customer-account/CustomerOrdersQuery.ts": {"bytes": 1164, "format": "esm", "imports": []}, "app/graphql/customer-account/CustomerAddressMutations.ts": {"bytes": 1355, "format": "esm", "imports": []}, "app/routes/($locale).api.$version.[graphql.json].tsx": {"bytes": 430, "format": "esm", "imports": []}, "node_modules/@shopify/hydrogen/dist/production/index.js": {"bytes": 92717, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen-react/dist/node-prod/index.mjs", "kind": "import-statement", "original": "@shopify/hydrogen-react"}, {"path": "node_modules/worktop/cookie/index.mjs", "kind": "import-statement", "original": "worktop/cookie"}, {"path": "node_modules/content-security-policy-builder/esm/mod.js", "kind": "import-statement", "original": "content-security-policy-builder"}, {"path": "node_modules/@shopify/hydrogen/dist/production/log-seo-tags-TY72EQWZ.js", "kind": "dynamic-import", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/@shopify/hydrogen/dist/production/log-seo-tags-TY72EQWZ.js"}]}, "app/routes/($locale).tsx": {"bytes": 525, "format": "esm", "imports": []}, "app/graphql/customer-account/CustomerDetailsQuery.ts": {"bytes": 733, "format": "esm", "imports": []}, "app/graphql/customer-account/CustomerUpdateMutation.ts": {"bytes": 493, "format": "esm", "imports": []}, "node_modules/react-router/dist/development/chunk-D4RADZKF.mjs": {"bytes": 372927, "format": "cjs", "imports": []}, "node_modules/react-dom/cjs/react-dom.production.min.js": {"bytes": 131685, "format": "cjs", "imports": [{"path": "node_modules/react/index.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/react/index.js"}, {"path": "node_modules/scheduler/index.js", "kind": "import-statement", "original": "C:/Users/<USER>/Desktop/br_push/big-river-coffee/node_modules/scheduler/index.js"}]}, "app/lib/session.ts": {"bytes": 1551, "format": "esm", "imports": [{"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}]}, "app/lib/redirect.ts": {"bytes": 538, "format": "esm", "imports": [{"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}]}, "app/components/GTMLoader.tsx": {"bytes": 1366, "format": "cjs", "imports": []}, "app/routes/($locale).cart.$lines.tsx": {"bytes": 1886, "format": "esm", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}]}, "app/routes/($locale).account._index.tsx": {"bytes": 445, "format": "esm", "imports": [{"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}]}, "app/routes/($locale).account.$.tsx": {"bytes": 294, "format": "esm", "imports": [{"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}]}, "app/routes/($locale).[sitemap.xml].tsx": {"bytes": 376, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}]}, "app/routes/($locale).account_.logout.tsx": {"bytes": 333, "format": "esm", "imports": [{"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}]}, "app/components/TrafficSourceTracker.tsx": {"bytes": 4750, "format": "cjs", "imports": [{"path": "app/hooks/useUTMTracking.ts", "kind": "import-statement", "original": "~/hooks/useUTMTracking"}]}, "app/routes/[robots.txt].tsx": {"bytes": 2874, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}]}, "app/routes/($locale).discount.$code.tsx": {"bytes": 1469, "format": "esm", "imports": [{"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}]}, "app/routes/($locale).sitemap.$type.$page[.xml].tsx": {"bytes": 602, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}]}, "app/components/PerformanceOptimizations.tsx": {"bytes": 6318, "format": "cjs", "imports": []}, "app/routes/($locale).collections.all-coffees.tsx": {"bytes": 473, "format": "esm", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}]}, "app/routes/($locale).account_.authorize.tsx": {"bytes": 575, "format": "esm", "imports": [{"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}]}, "app/routes/($locale).pages.nicaragua.tsx": {"bytes": 7403, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}]}, "app/routes/($locale).pages.subscriptions.tsx": {"bytes": 7032, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}]}, "app/lib/context.ts": {"bytes": 1543, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "node_modules/@shopify/admin-api-client/dist/index.mjs", "kind": "import-statement", "original": "@shopify/admin-api-client"}, {"path": "app/lib/session.ts", "kind": "import-statement", "original": "~/lib/session"}, {"path": "app/lib/fragments.ts", "kind": "import-statement", "original": "~/lib/fragments"}, {"path": "app/lib/i18n.ts", "kind": "import-statement", "original": "~/lib/i18n"}]}, "app/lib/variants.ts": {"bytes": 1126, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "app/components/UTMLink.tsx": {"bytes": 2843, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/lib/utm.ts", "kind": "import-statement", "original": "~/lib/utm"}]}, "app/components/PromoPopup.tsx": {"bytes": 4190, "format": "cjs", "imports": [{"path": "app/components/UTMLink.tsx", "kind": "import-statement", "original": "~/components/UTMLink"}]}, "app/components/ProductPrice.tsx": {"bytes": 395, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}]}, "app/components/Aside.tsx": {"bytes": 2081, "format": "cjs", "imports": []}, "app/components/PaginatedResourceSection.tsx": {"bytes": 1234, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}]}, "app/components/ProductFilters.tsx": {"bytes": 6678, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "app/components/ProductCard.tsx": {"bytes": 15107, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/lib/variants.ts", "kind": "import-statement", "original": "~/lib/variants"}, {"path": "app/components/AddToCartButton.tsx", "kind": "import-statement", "original": "~/components/AddToCartButton"}]}, "app/components/AddToCartButton.tsx": {"bytes": 1733, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}]}, "app/components/OptimizedVideo.tsx": {"bytes": 5550, "format": "cjs", "imports": []}, "app/routes/($locale).our-story.tsx": {"bytes": 32503, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "app/components/OptimizedVideo.tsx", "kind": "import-statement", "original": "~/components/OptimizedVideo"}, {"path": "app/lib/scrollAnimations.ts", "kind": "import-statement", "original": "~/lib/scrollAnimations"}]}, "app/routes/($locale).policies._index.tsx": {"bytes": 1405, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "app/routes/($locale).policies.$handle.tsx": {"bytes": 2332, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "app/routes/($locale).pages.$handle.tsx": {"bytes": 2344, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/lib/redirect.ts", "kind": "import-statement", "original": "~/lib/redirect"}]}, "server.ts": {"bytes": 1691, "format": "esm", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}, {"path": "app/lib/context.ts", "kind": "import-statement", "original": "~/lib/context"}, {"path": "\u0000virtual:react-router/server-build", "kind": "dynamic-import", "original": "\u0000virtual:react-router/server-build"}]}, "app/components/SubscriptionDropdown.tsx": {"bytes": 6957, "format": "cjs", "imports": [{"path": "app/components/AddToCartButton.tsx", "kind": "import-statement", "original": "./AddToCartButton"}, {"path": "app/components/Aside.tsx", "kind": "import-statement", "original": "./Aside"}]}, "app/hooks/useUTMTracking.ts": {"bytes": 1248, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/lib/utm.ts", "kind": "import-statement", "original": "~/lib/utm"}]}, "app/components/GoogleAnalytics.tsx": {"bytes": 9596, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/hooks/useUTMTracking.ts", "kind": "import-statement", "original": "~/hooks/useUTMTracking"}, {"path": "app/lib/utm.ts", "kind": "import-statement", "original": "~/lib/utm"}]}, "app/routes/($locale).pages.brew.tsx": {"bytes": 28351, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}]}, "app/routes/($locale).affiliate.tsx": {"bytes": 14021, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}]}, "app/components/CartMain.tsx": {"bytes": 3668, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/components/Aside.tsx", "kind": "import-statement", "original": "~/components/Aside"}, {"path": "app/components/CartLineItem.tsx", "kind": "import-statement", "original": "~/components/CartLineItem"}, {"path": "app/components/CartSummary.tsx", "kind": "import-statement", "original": "./CartSummary"}]}, "app/components/Footer.tsx": {"bytes": 10243, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "app/components/CartSummary.tsx": {"bytes": 7696, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}]}, "app/components/CartLineItem.tsx": {"bytes": 7151, "format": "cjs", "imports": [{"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/lib/variants.ts", "kind": "import-statement", "original": "~/lib/variants"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/components/ProductPrice.tsx", "kind": "import-statement", "original": "./ProductPrice"}, {"path": "app/components/Aside.tsx", "kind": "import-statement", "original": "./Aside"}]}, "app/components/SearchFormPredictive.tsx": {"bytes": 2262, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/components/Aside.tsx", "kind": "import-statement", "original": "./Aside"}]}, "app/routes/($locale).account.profile.tsx": {"bytes": 3524, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "app/graphql/customer-account/CustomerUpdateMutation.ts", "kind": "import-statement", "original": "~/graphql/customer-account/CustomerUpdateMutation"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "app/components/PageLayout.tsx": {"bytes": 8598, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/components/Aside.tsx", "kind": "import-statement", "original": "~/components/Aside"}, {"path": "app/components/Footer.tsx", "kind": "import-statement", "original": "~/components/Footer"}, {"path": "app/components/Header.tsx", "kind": "import-statement", "original": "~/components/Header"}, {"path": "app/components/CartMain.tsx", "kind": "import-statement", "original": "~/components/CartMain"}, {"path": "app/components/SearchFormPredictive.tsx", "kind": "import-statement", "original": "~/components/SearchFormPredictive"}, {"path": "app/components/SearchResultsPredictive.tsx", "kind": "import-statement", "original": "~/components/SearchResultsPredictive"}, {"path": "app/components/PromoPopup.tsx", "kind": "import-statement", "original": "~/components/PromoPopup"}, {"path": "app/components/TrafficSourceTracker.tsx", "kind": "import-statement", "original": "~/components/TrafficSourceTracker"}, {"path": "app/components/PerformanceOptimizations.tsx", "kind": "import-statement", "original": "~/components/PerformanceOptimizations"}]}, "app/routes/($locale).account.addresses.tsx": {"bytes": 13286, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/graphql/customer-account/CustomerAddressMutations.ts", "kind": "import-statement", "original": "~/graphql/customer-account/CustomerAddressMutations"}]}, "app/routes/($locale).blogs._index.tsx": {"bytes": 2868, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/components/PaginatedResourceSection.tsx", "kind": "import-statement", "original": "~/components/PaginatedResourceSection"}]}, "app/routes/($locale).search.tsx": {"bytes": 9190, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/components/SearchForm.tsx", "kind": "import-statement", "original": "~/components/SearchForm"}, {"path": "app/components/SearchResults.tsx", "kind": "import-statement", "original": "~/components/SearchResults"}, {"path": "app/lib/search.ts", "kind": "import-statement", "original": "~/lib/search"}]}, "app/components/SearchForm.tsx": {"bytes": 1561, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "app/components/VideoIntro.tsx": {"bytes": 5228, "format": "cjs", "imports": []}, "app/components/ProductForm.tsx": {"bytes": 6379, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/components/Aside.tsx", "kind": "import-statement", "original": "./Aside"}, {"path": "app/components/SubscriptionDropdown.tsx", "kind": "import-statement", "original": "./SubscriptionDropdown"}]}, "app/root.tsx": {"bytes": 8486, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/assets/favicon.svg", "kind": "import-statement", "original": "~/assets/favicon.svg"}, {"path": "app/lib/fragments.ts", "kind": "import-statement", "original": "~/lib/fragments"}, {"path": "app/styles/reset.css?url", "kind": "import-statement", "original": "~/styles/reset.css?url"}, {"path": "app/styles/app.css?url", "kind": "import-statement", "original": "~/styles/app.css?url"}, {"path": "app/styles/homepage.css?url", "kind": "import-statement", "original": "~/styles/homepage.css?url"}, {"path": "app/styles/tailwind.css?url", "kind": "import-statement", "original": "./styles/tailwind.css?url"}, {"path": "app/components/PageLayout.tsx", "kind": "import-statement", "original": "./components/PageLayout"}, {"path": "app/components/GoogleAnalytics.tsx", "kind": "import-statement", "original": "./components/GoogleAnalytics"}, {"path": "app/components/GTMLoader.tsx", "kind": "import-statement", "original": "./components/GTMLoader"}]}, "app/routes/($locale).blogs.$blogHandle._index.tsx": {"bytes": 4531, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/components/PaginatedResourceSection.tsx", "kind": "import-statement", "original": "~/components/PaginatedResourceSection"}, {"path": "app/lib/redirect.ts", "kind": "import-statement", "original": "~/lib/redirect"}]}, "app/routes/($locale).contact.tsx": {"bytes": 22870, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/lib/scrollAnimations.ts", "kind": "import-statement", "original": "~/lib/scrollAnimations"}]}, "app/routes/($locale).account_.register.tsx": {"bytes": 3885, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "app/routes/($locale).account.orders._index.tsx": {"bytes": 2506, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/graphql/customer-account/CustomerOrdersQuery.ts", "kind": "import-statement", "original": "~/graphql/customer-account/CustomerOrdersQuery"}, {"path": "app/components/PaginatedResourceSection.tsx", "kind": "import-statement", "original": "~/components/PaginatedResourceSection"}]}, "app/components/SearchResults.tsx": {"bytes": 4226, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/lib/search.ts", "kind": "import-statement", "original": "~/lib/search"}]}, "app/routes/($locale).collections._index.tsx": {"bytes": 3427, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/components/PaginatedResourceSection.tsx", "kind": "import-statement", "original": "~/components/PaginatedResourceSection"}]}, "app/routes/($locale)._index.tsx": {"bytes": 23181, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/components/VideoIntro.tsx", "kind": "import-statement", "original": "~/components/VideoIntro"}, {"path": "app/components/OptimizedVideo.tsx", "kind": "import-statement", "original": "~/components/OptimizedVideo"}]}, "app/routes/($locale).blogs.$blogHandle.$articleHandle.tsx": {"bytes": 3473, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/lib/redirect.ts", "kind": "import-statement", "original": "~/lib/redirect"}]}, "app/routes/($locale).account_.login.tsx": {"bytes": 7902, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}]}, "app/routes/($locale).account.tsx": {"bytes": 6846, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "app/graphql/customer-account/CustomerDetailsQuery.ts", "kind": "import-statement", "original": "~/graphql/customer-account/CustomerDetailsQuery"}]}, "app/components/SearchResultsPredictive.tsx": {"bytes": 10537, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/lib/search.ts", "kind": "import-statement", "original": "~/lib/search"}, {"path": "app/components/Aside.tsx", "kind": "import-statement", "original": "./Aside"}]}, "app/components/Header.tsx": {"bytes": 12299, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/components/Aside.tsx", "kind": "import-statement", "original": "~/components/Aside"}]}, "app/routes/($locale).cart.tsx": {"bytes": 3070, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}, {"path": "app/components/CartMain.tsx", "kind": "import-statement", "original": "~/components/CartMain"}]}, "app/routes/($locale).products.roasters-box.tsx": {"bytes": 12875, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/components/Aside.tsx", "kind": "import-statement", "original": "~/components/Aside"}, {"path": "app/components/SubscriptionDropdown.tsx", "kind": "import-statement", "original": "~/components/SubscriptionDropdown"}]}, "app/routes/($locale).account.orders.$id.tsx": {"bytes": 5406, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/graphql/customer-account/CustomerOrderQuery.ts", "kind": "import-statement", "original": "~/graphql/customer-account/CustomerOrderQuery"}]}, "app/routes/($locale).products.$handle.tsx": {"bytes": 19004, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/components/ProductPrice.tsx", "kind": "import-statement", "original": "~/components/ProductPrice"}, {"path": "app/components/ProductForm.tsx", "kind": "import-statement", "original": "~/components/ProductForm"}, {"path": "app/lib/redirect.ts", "kind": "import-statement", "original": "~/lib/redirect"}]}, "app/entry.server.tsx": {"bytes": 2685, "format": "cjs", "imports": [{"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/isbot/index.mjs", "kind": "import-statement", "original": "isbot"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}]}, "app/routes/($locale).collections.$handle.tsx": {"bytes": 13352, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/@shopify/remix-oxygen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/remix-oxygen"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/lib/redirect.ts", "kind": "import-statement", "original": "~/lib/redirect"}, {"path": "app/components/ProductCard.tsx", "kind": "import-statement", "original": "~/components/ProductCard"}, {"path": "app/components/ProductFilters.tsx", "kind": "import-statement", "original": "~/components/ProductFilters"}]}, "app/routes/($locale).collections.all.tsx": {"bytes": 110822, "format": "cjs", "imports": [{"path": "\u0000virtual:react-router/with-props", "kind": "import-statement", "original": "virtual:react-router/with-props"}, {"path": "node_modules/react-router/dist/development/index.mjs", "kind": "import-statement", "original": "react-router"}, {"path": "node_modules/@shopify/hydrogen/dist/production/index.js", "kind": "import-statement", "original": "@shopify/hydrogen"}, {"path": "app/components/ProductCard.tsx", "kind": "import-statement", "original": "~/components/ProductCard"}, {"path": "app/components/AddToCartButton.tsx", "kind": "import-statement", "original": "~/components/AddToCartButton"}, {"path": "app/components/Aside.tsx", "kind": "import-statement", "original": "~/components/Aside"}, {"path": "app/components/OptimizedVideo.tsx", "kind": "import-statement", "original": "~/components/OptimizedVideo"}, {"path": "app/lib/scrollAnimations.ts", "kind": "import-statement", "original": "~/lib/scrollAnimations"}]}}, "outputs": {"dist/server/index.js": {"imports": [], "exports": ["default"], "entryPoint": "server.ts", "bytes": 792952, "inputs": {"node_modules/@shopify/hydrogen-react/dist/node-prod/codegen.helpers.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-queries.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/@xstate/fsm/es/index.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector2.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector.production.min.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector.development.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/index.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/use-sync-external-store-shim.production.min.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/use-sync-external-store-shim.development.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/graphql-client/dist/index.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/admin-api-client/dist/index.mjs": {"bytesInOutput": 0}, "node_modules/react-router/dist/development/index.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/parse-metafield.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/_virtual/with-selector.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/@xstate/react/es/useConstant.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineQuantity.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineProvider.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ExternalVideo.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/BaseButton.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/RichText.components.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/shim/with-selector.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/shim/index.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartCost.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ProductPrice.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/useCartActions.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.min.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ModelViewer.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ProductProvider.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/RichText.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/Video.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/admin-api-client/dist/rest/client.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ShopPayButton.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-hooks.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/BuyNowButton.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartCheckoutButton.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/MediaFile.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/useCartAPIStateMachine.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/node_modules/@xstate/react/es/fsm.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/AddToCartButton.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartLineQuantityAdjustButton.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/CartProvider.mjs": {"bytesInOutput": 0}, "node_modules/@shopify/hydrogen-react/dist/node-prod/index.mjs": {"bytesInOutput": 0}, "node_modules/react/cjs/react.production.min.js": {"bytesInOutput": 7298}, "app/assets/favicon.svg": {"bytesInOutput": 43}, "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-constants.mjs": {"bytesInOutput": 367}, "node_modules/@shopify/hydrogen-react/dist/node-prod/optionValueDecoder.mjs": {"bytesInOutput": 1302}, "node_modules/worktop/cookie/index.mjs": {"bytesInOutput": 902}, "node_modules/@shopify/admin-api-client/dist/rest/types.mjs": {"bytesInOutput": 104}, "node_modules/@shopify/graphql-client/dist/api-client-utilities/validations.mjs": {"bytesInOutput": 752}, "node_modules/@shopify/graphql-client/dist/api-client-utilities/utilities.mjs": {"bytesInOutput": 347}, "node_modules/@shopify/hydrogen-react/dist/node-prod/cart-constants.mjs": {"bytesInOutput": 208}, "node_modules/@shopify/hydrogen/dist/production/log-seo-tags-TY72EQWZ.js": {"bytesInOutput": 1322}, "node_modules/content-security-policy-builder/esm/mod.js": {"bytesInOutput": 409}, "node_modules/@shopify/hydrogen-react/dist/node-prod/storefront-api-constants.mjs": {"bytesInOutput": 31}, "node_modules/@shopify/hydrogen-react/dist/node-prod/packages/hydrogen-react/package.json.mjs": {"bytesInOutput": 26}, "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-utils.mjs": {"bytesInOutput": 741}, "node_modules/@shopify/admin-api-client/dist/constants.mjs": {"bytesInOutput": 149}, "node_modules/isbot/index.mjs": {"bytesInOutput": 2171}, "node_modules/cookie/dist/index.js": {"bytesInOutput": 2609}, "node_modules/scheduler/cjs/scheduler.production.min.js": {"bytesInOutput": 4010}, "node_modules/react/cjs/react-jsx-runtime.production.min.js": {"bytesInOutput": 923}, "node_modules/react/index.js": {"bytesInOutput": 150}, "node_modules/react-dom/index.js": {"bytesInOutput": 394}, "node_modules/react/jsx-runtime.js": {"bytesInOutput": 190}, "node_modules/react-dom/server.browser.js": {"bytesInOutput": 601}, "app/styles/tailwind.css?url": {"bytesInOutput": 216}, "app/styles/homepage.css?url": {"bytesInOutput": 219}, "app/styles/app.css?url": {"bytesInOutput": 204}, "node_modules/@shopify/hydrogen-react/dist/node-prod/getProductOptions.mjs": {"bytesInOutput": 4102}, "app/styles/reset.css?url": {"bytesInOutput": 210}, "node_modules/@shopify/hydrogen-react/dist/node-prod/storefront-client.mjs": {"bytesInOutput": 2163}, "node_modules/@shopify/graphql-client/dist/graphql-client/utilities.mjs": {"bytesInOutput": 1117}, "node_modules/@shopify/admin-api-client/dist/validations.mjs": {"bytesInOutput": 250}, "node_modules/@shopify/remix-oxygen/dist/production/index.js": {"bytesInOutput": 582}, "node_modules/@shopify/hydrogen-react/dist/node-prod/load-script.mjs": {"bytesInOutput": 615}, "node_modules/@shopify/graphql-client/dist/graphql-client/http-fetch.mjs": {"bytesInOutput": 932}, "node_modules/@shopify/hydrogen-react/dist/node-prod/cookies-utils.mjs": {"bytesInOutput": 673}, "\u0000virtual:react-router/with-props": {"bytesInOutput": 392}, "node_modules/@shopify/hydrogen-react/dist/node-prod/useSelectedOptionInUrlParam.mjs": {"bytesInOutput": 384}, "node_modules/@shopify/hydrogen-react/dist/node-prod/Money.mjs": {"bytesInOutput": 643}, "node_modules/@shopify/hydrogen-react/dist/node-prod/useMoney.mjs": {"bytesInOutput": 2144}, "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-schema-trekkie-storefront-page-view.mjs": {"bytesInOutput": 1044}, "node_modules/@shopify/graphql-client/dist/graphql-client/graphql-client.mjs": {"bytesInOutput": 5484}, "node_modules/@shopify/admin-api-client/dist/graphql/client.mjs": {"bytesInOutput": 1273}, "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics-schema-custom-storefront-customer-tracking.mjs": {"bytesInOutput": 3673}, "node_modules/@shopify/hydrogen-react/dist/node-prod/useShopifyCookies.mjs": {"bytesInOutput": 735}, "node_modules/@shopify/hydrogen-react/dist/node-prod/ShopifyProvider.mjs": {"bytesInOutput": 467}, "node_modules/@shopify/hydrogen-react/dist/node-prod/analytics.mjs": {"bytesInOutput": 2845}, "\u0000virtual:react-router/server-build": {"bytesInOutput": 7300}, "node_modules/@shopify/hydrogen-react/dist/node-prod/flatten-connection.mjs": {"bytesInOutput": 375}, "node_modules/@shopify/graphql-client/dist/api-client-utilities/api-versions.mjs": {"bytesInOutput": 605}, "\u0000virtual:react-router/server-manifest": {"bytesInOutput": 35842}, "node_modules/@shopify/graphql-client/dist/graphql-client/constants.mjs": {"bytesInOutput": 790}, "app/lib/fragments.ts": {"bytesInOutput": 3579}, "node_modules/set-cookie-parser/lib/set-cookie.js": {"bytesInOutput": 2389}, "node_modules/scheduler/index.js": {"bytesInOutput": 178}, "node_modules/@shopify/hydrogen-react/dist/node-prod/Image.mjs": {"bytesInOutput": 4594}, "app/lib/utm.ts": {"bytesInOutput": 1652}, "node_modules/react-dom/cjs/react-dom-server-legacy.browser.production.min.js": {"bytesInOutput": 34761}, "node_modules/react-dom/cjs/react-dom-server.browser.production.min.js": {"bytesInOutput": 35126}, "app/lib/scrollAnimations.ts": {"bytesInOutput": 373}, "app/lib/i18n.ts": {"bytesInOutput": 229}, "app/lib/search.ts": {"bytesInOutput": 310}, "app/graphql/customer-account/CustomerOrderQuery.ts": {"bytesInOutput": 1393}, "app/routes/($locale).$.tsx": {"bytesInOutput": 170}, "app/graphql/customer-account/CustomerOrdersQuery.ts": {"bytesInOutput": 902}, "app/graphql/customer-account/CustomerAddressMutations.ts": {"bytesInOutput": 1027}, "app/routes/($locale).api.$version.[graphql.json].tsx": {"bytesInOutput": 257}, "node_modules/@shopify/hydrogen/dist/production/index.js": {"bytesInOutput": 66801}, "app/routes/($locale).tsx": {"bytesInOutput": 207}, "app/graphql/customer-account/CustomerDetailsQuery.ts": {"bytesInOutput": 547}, "app/graphql/customer-account/CustomerUpdateMutation.ts": {"bytesInOutput": 475}, "node_modules/react-router/dist/development/chunk-D4RADZKF.mjs": {"bytesInOutput": 113067}, "node_modules/react-dom/cjs/react-dom.production.min.js": {"bytesInOutput": 130071}, "app/lib/session.ts": {"bytesInOutput": 602}, "app/lib/redirect.ts": {"bytesInOutput": 209}, "app/components/GTMLoader.tsx": {"bytesInOutput": 547}, "app/routes/($locale).cart.$lines.tsx": {"bytesInOutput": 694}, "app/routes/($locale).account._index.tsx": {"bytesInOutput": 237}, "app/routes/($locale).account.$.tsx": {"bytesInOutput": 109}, "app/routes/($locale).[sitemap.xml].tsx": {"bytesInOutput": 166}, "app/routes/($locale).account_.logout.tsx": {"bytesInOutput": 119}, "app/components/TrafficSourceTracker.tsx": {"bytesInOutput": 2309}, "app/routes/[robots.txt].tsx": {"bytesInOutput": 2278}, "app/routes/($locale).discount.$code.tsx": {"bytesInOutput": 404}, "app/routes/($locale).sitemap.$type.$page[.xml].tsx": {"bytesInOutput": 307}, "app/components/PerformanceOptimizations.tsx": {"bytesInOutput": 1594}, "app/routes/($locale).collections.all-coffees.tsx": {"bytesInOutput": 227}, "app/routes/($locale).account_.authorize.tsx": {"bytesInOutput": 192}, "app/routes/($locale).pages.nicaragua.tsx": {"bytesInOutput": 6711}, "app/routes/($locale).pages.subscriptions.tsx": {"bytesInOutput": 6688}, "app/lib/context.ts": {"bytesInOutput": 538}, "app/lib/variants.ts": {"bytesInOutput": 467}, "app/components/UTMLink.tsx": {"bytesInOutput": 743}, "app/components/PromoPopup.tsx": {"bytesInOutput": 3106}, "app/components/ProductPrice.tsx": {"bytesInOutput": 212}, "app/components/Aside.tsx": {"bytesInOutput": 1105}, "app/components/PaginatedResourceSection.tsx": {"bytesInOutput": 569}, "app/components/ProductFilters.tsx": {"bytesInOutput": 4517}, "app/components/ProductCard.tsx": {"bytesInOutput": 11917}, "app/components/AddToCartButton.tsx": {"bytesInOutput": 1157}, "app/components/OptimizedVideo.tsx": {"bytesInOutput": 2278}, "app/routes/($locale).our-story.tsx": {"bytesInOutput": 28213}, "app/routes/($locale).policies._index.tsx": {"bytesInOutput": 1125}, "app/routes/($locale).policies.$handle.tsx": {"bytesInOutput": 1711}, "app/routes/($locale).pages.$handle.tsx": {"bytesInOutput": 1195}, "server.ts": {"bytesInOutput": 432}, "app/components/SubscriptionDropdown.tsx": {"bytesInOutput": 4279}, "app/hooks/useUTMTracking.ts": {"bytesInOutput": 426}, "app/components/GoogleAnalytics.tsx": {"bytesInOutput": 4309}, "app/routes/($locale).pages.brew.tsx": {"bytesInOutput": 27699}, "app/routes/($locale).affiliate.tsx": {"bytesInOutput": 13230}, "app/components/CartMain.tsx": {"bytesInOutput": 2410}, "app/components/Footer.tsx": {"bytesInOutput": 8017}, "app/components/CartSummary.tsx": {"bytesInOutput": 2604}, "app/components/CartLineItem.tsx": {"bytesInOutput": 4316}, "app/components/SearchFormPredictive.tsx": {"bytesInOutput": 723}, "app/routes/($locale).account.profile.tsx": {"bytesInOutput": 2071}, "app/components/PageLayout.tsx": {"bytesInOutput": 4682}, "app/routes/($locale).account.addresses.tsx": {"bytesInOutput": 7867}, "app/routes/($locale).blogs._index.tsx": {"bytesInOutput": 1463}, "app/routes/($locale).search.tsx": {"bytesInOutput": 6801}, "app/components/SearchForm.tsx": {"bytesInOutput": 467}, "app/components/VideoIntro.tsx": {"bytesInOutput": 2530}, "app/components/ProductForm.tsx": {"bytesInOutput": 3060}, "app/root.tsx": {"bytesInOutput": 4164}, "app/routes/($locale).blogs.$blogHandle._index.tsx": {"bytesInOutput": 2646}, "app/routes/($locale).contact.tsx": {"bytesInOutput": 17854}, "app/routes/($locale).account_.register.tsx": {"bytesInOutput": 3053}, "app/routes/($locale).account.orders._index.tsx": {"bytesInOutput": 1859}, "app/components/SearchResults.tsx": {"bytesInOutput": 2832}, "app/routes/($locale).collections._index.tsx": {"bytesInOutput": 1846}, "app/routes/($locale)._index.tsx": {"bytesInOutput": 14533}, "app/routes/($locale).blogs.$blogHandle.$articleHandle.tsx": {"bytesInOutput": 2004}, "app/routes/($locale).account_.login.tsx": {"bytesInOutput": 5644}, "app/routes/($locale).account.tsx": {"bytesInOutput": 5799}, "app/components/SearchResultsPredictive.tsx": {"bytesInOutput": 6629}, "app/components/Header.tsx": {"bytesInOutput": 7852}, "app/routes/($locale).cart.tsx": {"bytesInOutput": 1443}, "app/routes/($locale).products.roasters-box.tsx": {"bytesInOutput": 9208}, "app/routes/($locale).account.orders.$id.tsx": {"bytesInOutput": 4455}, "app/routes/($locale).products.$handle.tsx": {"bytesInOutput": 14965}, "app/entry.server.tsx": {"bytesInOutput": 1241}, "app/routes/($locale).collections.$handle.tsx": {"bytesInOutput": 9193}, "app/routes/($locale).collections.all.tsx": {"bytesInOutput": 53471}}}}}