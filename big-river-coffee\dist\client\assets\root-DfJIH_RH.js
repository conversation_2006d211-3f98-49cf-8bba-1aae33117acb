import{w as te,a as re}from"./with-props-ZU_SrDpf.js";import{j as e}from"./jsx-runtime-D5QEUsP9.js";import{J as se,G as $,$ as ie,s as ne}from"./index-BVkVvY9o.js";import{N as w,r as m,E as A,G as oe,d as V,c as ae,L as M,b as ce,i as Y,H as le,M as ue,I as de,S as me,J as he,O as pe,h as fe,K as ge}from"./chunk-D4RADZKF-DNKU_WG6.js";import{u as E,A as L}from"./Aside-HLZLr7KT.js";import{C as xe}from"./CartMain-BBIJRGW2.js";import{g as we,u as U}from"./search-DOeYwaXi.js";import{I as F}from"./Image-BhFgnyoc.js";import{M as ye}from"./Money-BAfw1cBI.js";import"./variants-J2GcD6hP.js";import"./ProductPrice-C6YWsE4Q.js";const ve="https://cdn.shopify.com/oxygen-v2/43488/38477/80923/2045476/assets/favicon-DZkC1E9c.svg",je="https://cdn.shopify.com/oxygen-v2/43488/38477/80923/2045476/assets/reset-BKioPaen.css",be="https://cdn.shopify.com/oxygen-v2/43488/38477/80923/2045476/assets/app-CPC0BIIK.css",_e="https://cdn.shopify.com/oxygen-v2/43488/38477/80923/2045476/assets/homepage-7uZSHaEk.css",Ne="https://cdn.shopify.com/oxygen-v2/43488/38477/80923/2045476/assets/tailwind-CmBg5fsC.css";function ke({footer:t,header:r,publicStoreDomain:a}){const s=new Date().getFullYear();return e.jsxs("footer",{className:"text-white mt-auto",style:{backgroundColor:"#3A5C5C"},children:[e.jsx("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-12",children:[e.jsxs("div",{className:"col-span-1 md:col-span-2",children:[e.jsx(w,{to:"/",className:"inline-block mb-6",children:e.jsx("img",{src:"/Logo_Official.svg",alt:"Big River Coffee",className:"h-20 w-auto"})}),e.jsx("p",{className:"text-white mb-12 text-base leading-relaxed max-w-md",children:"Premium coffee for adventurers. Ethically sourced from mountain regions, expertly roasted for those who live life to the fullest."}),e.jsxs("div",{className:"flex space-x-4 mt-4",children:[e.jsx("a",{href:"https://www.instagram.com/bigriver.coffee/",target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200","aria-label":"Follow us on Instagram",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})}),e.jsx("a",{href:"https://www.facebook.com/bigriverco/",target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200","aria-label":"Follow us on Facebook",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})}),e.jsx("a",{href:"https://www.tiktok.com/@bigriver.coffee",target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200","aria-label":"Follow us on TikTok",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-.88-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z"})})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white text-lg font-semibold mb-6",children:"Shop"}),e.jsxs("ul",{className:"space-y-3",children:[e.jsx("li",{children:e.jsx(w,{to:"/collections/all",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Coffee"})}),e.jsx("li",{children:e.jsx(w,{to:"/collections/all",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"K-Cups"})}),e.jsx("li",{children:e.jsx(w,{to:"/collections/all",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Subscriptions"})}),e.jsx("li",{children:e.jsx(w,{to:"/our-story",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Our Story"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white text-lg font-semibold mb-6",children:"Help"}),e.jsxs("ul",{className:"space-y-3",children:[e.jsx("li",{children:e.jsx(w,{to:"/policies/shipping-policy",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Shipping Info"})}),e.jsx("li",{children:e.jsx(w,{to:"/policies/refund-policy",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Returns"})}),e.jsx("li",{children:e.jsx(w,{to:"/account",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"My Account"})})]})]})]})}),e.jsx("div",{className:"border-t border-white/20",children:e.jsx("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:e.jsxs("div",{className:"md:flex md:items-center md:justify-between",children:[e.jsxs("div",{className:"text-sm text-white",children:["© ",s," Big River Coffee. All rights reserved."]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(m.Suspense,{children:e.jsx(A,{resolve:t,children:o=>{var l;return(o==null?void 0:o.menu)&&((l=r.shop.primaryDomain)==null?void 0:l.url)&&e.jsx(Se,{menu:o.menu,primaryDomainUrl:r.shop.primaryDomain.url,publicStoreDomain:a})}})})})]})})})]})}function Se({menu:t,primaryDomainUrl:r,publicStoreDomain:a}){return e.jsx("nav",{role:"navigation",children:e.jsx("ul",{className:"flex flex-wrap gap-6",children:(t||Ce).items.map(s=>{if(!s.url)return null;const o=s.url.includes("myshopify.com")||s.url.includes(a)||s.url.includes(r)?new URL(s.url).pathname:s.url,l=!o.startsWith("/");return e.jsx("li",{children:l?e.jsx("a",{href:o,rel:"noopener noreferrer",target:"_blank",className:"text-sm text-white hover:text-orange-300 transition-colors duration-200",children:s.title}):e.jsx(w,{end:!0,prefetch:"intent",to:o,className:"text-sm text-white hover:text-orange-300 transition-colors duration-200",children:s.title})},s.id)})})})}const Ce={items:[{id:"gid://shopify/MenuItem/461633060920",resourceId:"gid://shopify/ShopPolicy/23358046264",tags:[],title:"Privacy Policy",type:"SHOP_POLICY",url:"/policies/privacy-policy",items:[]},{id:"gid://shopify/MenuItem/461633093688",resourceId:"gid://shopify/ShopPolicy/23358013496",tags:[],title:"Refund Policy",type:"SHOP_POLICY",url:"/policies/refund-policy",items:[]},{id:"gid://shopify/MenuItem/461633126456",resourceId:"gid://shopify/ShopPolicy/23358111800",tags:[],title:"Shipping Policy",type:"SHOP_POLICY",url:"/policies/shipping-policy",items:[]},{id:"gid://shopify/MenuItem/461633159224",resourceId:"gid://shopify/ShopPolicy/23358079032",tags:[],title:"Terms of Service",type:"SHOP_POLICY",url:"/policies/terms-of-service",items:[]}]};function Te({header:t,isLoggedIn:r,cart:a,publicStoreDomain:s}){const{shop:o,menu:l}=t,[i,n]=m.useState(!1),[c,u]=m.useState(!1),[d,h]=m.useState(0);return m.useEffect(()=>{const g=()=>{const p=window.scrollY;n(p>50),p>150?u(!0):u(!1),h(p)};return window.addEventListener("scroll",g),()=>window.removeEventListener("scroll",g)},[d]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"fixed top-0 left-0 right-0 z-50 text-white py-1.5 px-4",style:{backgroundColor:"#7895a4"},children:e.jsx("div",{className:"max-w-7xl mx-auto text-center",children:e.jsxs("p",{className:"text-sm font-semibold",children:["🔥 30% OFF EVERYTHING WITH CODE"," ",e.jsx("span",{className:"bg-white px-2 py-1 rounded font-bold text-xs",style:{color:"#7895a4"},children:"SUMMER30"})," ","🔥"]})})}),e.jsx("header",{className:`fixed left-0 right-0 z-40 transition-all duration-300 ${c?"translate-y-0":"-translate-y-full"}`,style:{top:"32px",height:"calc(var(--header-height) - 32px)",backgroundColor:"#3A5C5C",background:"#3A5C5C"},children:e.jsx("div",{className:"max-w-7xl mx-auto relative px-4 sm:px-6 lg:px-8 h-full",children:e.jsxs("div",{className:"flex items-center justify-between h-full",children:[e.jsx("div",{className:"flex justify-start items-center h-full",children:e.jsx(w,{to:"/",prefetch:"intent",end:!0,className:"relative group flex items-center h-full",children:e.jsx("img",{src:"/headerlogo.svg",alt:"Big River Coffee",className:"transition-all duration-300 group-hover:scale-105 h-24 sm:h-32 lg:h-36 w-auto",width:"auto",height:"140"})})}),e.jsx("div",{className:"hidden md:flex items-center space-x-8",children:e.jsx(K,{menu:null,viewport:"desktop",primaryDomainUrl:t.shop.primaryDomain.url,publicStoreDomain:s})}),e.jsx("div",{className:"flex-1 flex justify-end",children:e.jsx(Pe,{isLoggedIn:r,cart:a})})]})})})]})}function K({menu:t,primaryDomainUrl:r,viewport:a,publicStoreDomain:s}){var l;const{close:o}=E();return a==="mobile"?(console.log("Mobile menu rendering:",{menuExists:!!t,fallbackItems:O.items.length,menuItems:((l=t==null?void 0:t.items)==null?void 0:l.length)||0}),e.jsxs("nav",{className:"header-menu-mobile",role:"navigation",children:[e.jsx(w,{end:!0,onClick:o,prefetch:"intent",to:"/",className:"header-menu-item",children:"Home"}),O.items.map(i=>{if(!i.url)return null;let n=i.url;if(i.url.includes("myshopify.com")||i.url.includes(s)||i.url.includes(r))try{n=new URL(i.url).pathname}catch{n=i.url}return(n.includes("/products/big-river-k-cups")||i.title&&i.title.toLowerCase().includes("k-cup"))&&(n="/collections/all?section=kcups"),n.includes("#section-subscriptions")&&(n="/collections/all?section=subscriptions"),e.jsx(w,{end:!0,onClick:o,prefetch:"intent",to:n,className:"header-menu-item",children:i.title},i.id)})]})):e.jsx("nav",{className:"flex items-center space-x-8",role:"navigation",children:O.items.map(i=>{if(!i.url)return null;let n=i.url;if(i.url.includes("myshopify.com")||i.url.includes(s)||i.url.includes(r))try{n=new URL(i.url).pathname}catch{n=i.url}return(n.includes("/products/big-river-k-cups")||i.title&&i.title.toLowerCase().includes("k-cup"))&&(n="/collections/all?section=kcups"),n.includes("#section-subscriptions")&&(n="/collections/all?section=subscriptions"),e.jsx(w,{end:!0,prefetch:"intent",to:n,className:({isActive:c})=>`text-white hover:text-orange-300 transition-colors duration-200 font-medium relative subheader ${c?"text-orange-300":"text-white"}`,children:i.title},i.id)})})}function Pe({isLoggedIn:t,cart:r}){return e.jsxs("nav",{className:"flex items-center space-x-4",role:"navigation",children:[e.jsxs("div",{className:"hidden md:flex items-center space-x-4",children:[e.jsx(w,{prefetch:"intent",to:"/account",className:"text-white hover:text-orange-300 transition-colors duration-200 font-medium",children:e.jsx(m.Suspense,{fallback:"Sign in",children:e.jsx(A,{resolve:t,errorElement:"Sign in",children:a=>a?"Account":"Sign in"})})}),e.jsx(Ee,{})]}),e.jsx(Ie,{cart:r}),e.jsx("div",{className:"md:hidden",children:e.jsx(Me,{})})]})}function Me(){const{open:t}=E();return e.jsx("button",{className:"text-white hover:text-orange-300 transition-colors duration-200 p-2",onClick:()=>t("mobile"),"aria-label":"Open mobile menu",style:{color:"white"},children:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}function Ee(){const{open:t}=E();return e.jsx("button",{className:"text-white hover:text-orange-300 transition-colors duration-200 p-2",onClick:()=>t("search"),"aria-label":"Open search",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})}function J({count:t}){const{open:r}=E(),{publish:a,shop:s,cart:o,prevCart:l}=$();return e.jsxs("button",{onClick:i=>{i.preventDefault(),r("cart"),a("cart_viewed",{cart:o,prevCart:l,shop:s,url:window.location.href||""})},className:"relative text-white hover:text-orange-300 transition-colors duration-200 p-2","aria-label":`Cart with ${t||0} items`,children:[e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8"})}),t!==null&&t>0&&e.jsx("span",{className:"absolute -top-1 -right-1 bg-amber-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold",children:t})]})}function Ie({cart:t}){return e.jsx(m.Suspense,{fallback:e.jsx(J,{count:null}),children:e.jsx(A,{resolve:t,children:e.jsx(Ae,{})})})}function Ae(){const t=oe(),r=se(t);return e.jsx(J,{count:(r==null?void 0:r.totalQuantity)??0})}const O={items:[{id:"gid://shopify/MenuItem/461609500728",resourceId:null,tags:[],title:"Coffee",type:"HTTP",url:"/collections/all",items:[]},{id:"gid://shopify/MenuItem/461609500729",resourceId:null,tags:[],title:"K-Cups",type:"HTTP",url:"/collections/all?section=kcups",items:[]},{id:"gid://shopify/MenuItem/461609500731",resourceId:null,tags:[],title:"Subscriptions",type:"HTTP",url:"/collections/all#section-subscriptions",items:[]},{id:"gid://shopify/MenuItem/461609500730",resourceId:null,tags:[],title:"Brew",type:"HTTP",url:"/pages/brew",items:[]},{id:"gid://shopify/MenuItem/461609599032",resourceId:"gid://shopify/Page/92591030328",tags:[],title:"Our Story",type:"PAGE",url:"/our-story",items:[]},{id:"gid://shopify/MenuItem/461609566264",resourceId:null,tags:[],title:"Contact",type:"HTTP",url:"/contact",items:[]},{id:"gid://shopify/MenuItem/461609566265",resourceId:null,tags:[],title:"Affiliate",type:"HTTP",url:"/affiliate",items:[]}]},R="/search";function Le({children:t,className:r="predictive-search-form",...a}){const s=V({key:"search"}),o=m.useRef(null),l=ae(),i=E();function n(d){var h;d.preventDefault(),d.stopPropagation(),(h=o==null?void 0:o.current)!=null&&h.value&&o.current.blur()}function c(){var h;const d=(h=o==null?void 0:o.current)==null?void 0:h.value;l(R+(d?`?q=${d}`:"")),i.close()}function u(d){s.submit({q:d.target.value||"",limit:5,predictive:!0},{method:"GET",action:R})}return m.useEffect(()=>{var d;(d=o==null?void 0:o.current)==null||d.setAttribute("type","search")},[]),typeof t!="function"?null:e.jsx(s.Form,{...a,className:r,onSubmit:n,children:t({inputRef:o,fetcher:s,fetchResults:u,goToSearch:c})})}function y({children:t}){const r=E(),{term:a,inputRef:s,fetcher:o,total:l,items:i}=Be();function n(){s.current&&(s.current.blur(),s.current.value="")}function c(){n(),r.close()}return t({items:i,closeSearch:c,inputRef:s,state:o.state,term:a,total:l})}y.Articles=Ue;y.Collections=Oe;y.Pages=Re;y.Products=Fe;y.Queries=De;y.Empty=He;function Ue({term:t,articles:r,closeSearch:a}){return r.length?e.jsxs("div",{className:"predictive-search-result",children:[e.jsx("h5",{children:"Articles"}),e.jsx("ul",{children:r.map(s=>{var l;const o=U({baseUrl:`/blogs/${s.blog.handle}/${s.handle}`,trackingParams:s.trackingParameters,term:t.current??""});return e.jsx("li",{className:"predictive-search-result-item",children:e.jsxs(M,{onClick:a,to:o,children:[((l=s.image)==null?void 0:l.url)&&e.jsx(F,{alt:s.image.altText??"",src:s.image.url,width:50,height:50}),e.jsx("div",{children:e.jsx("span",{children:s.title})})]})},s.id)})})]},"articles"):null}function Oe({term:t,collections:r,closeSearch:a}){return r.length?e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-semibold text-army-700 mb-3 uppercase tracking-wide",children:"Collections"}),e.jsx("div",{className:"space-y-2",children:r.map(s=>{var l;const o=U({baseUrl:`/collections/${s.handle}`,trackingParams:s.trackingParameters,term:t.current});return e.jsx(M,{onClick:a,to:o,className:"block p-3 bg-white rounded-lg border border-army-100 hover:border-army-300 hover:bg-army-50 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[((l=s.image)==null?void 0:l.url)&&e.jsx(F,{alt:s.image.altText??"",src:s.image.url,width:32,height:32,className:"w-8 h-8 object-cover rounded"}),e.jsx("span",{className:"text-gray-900 font-medium",children:s.title})]}),e.jsx("svg",{className:"w-4 h-4 text-army-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})},s.id)})})]},"collections"):null}function Re({term:t,pages:r,closeSearch:a}){return r.length?e.jsxs("div",{className:"predictive-search-result",children:[e.jsx("h5",{children:"Pages"}),e.jsx("ul",{children:r.map(s=>{const o=U({baseUrl:`/pages/${s.handle}`,trackingParams:s.trackingParameters,term:t.current});return e.jsx("li",{className:"predictive-search-result-item",children:e.jsx(M,{onClick:a,to:o,children:e.jsx("div",{children:e.jsx("span",{children:s.title})})})},s.id)})})]},"pages"):null}function Fe({term:t,products:r,closeSearch:a}){return r.length?e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-semibold text-army-700 mb-3 uppercase tracking-wide",children:"Products"}),e.jsx("div",{className:"space-y-3",children:r.map(s=>{var n,c;const o=U({baseUrl:`/products/${s.handle}`,trackingParams:s.trackingParameters,term:t.current}),l=(n=s==null?void 0:s.selectedOrFirstAvailableVariant)==null?void 0:n.price,i=(c=s==null?void 0:s.selectedOrFirstAvailableVariant)==null?void 0:c.image;return e.jsx(M,{to:o,onClick:a,className:"block p-3 bg-white rounded-lg border border-army-100 hover:border-army-300 hover:bg-army-50 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[i&&e.jsx("div",{className:"flex-shrink-0",children:e.jsx(F,{alt:i.altText??"",src:i.url,width:48,height:48,className:"w-12 h-12 object-cover rounded-lg"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:s.title}),l&&e.jsx("p",{className:"text-sm text-army-600 font-semibold",children:e.jsx(ye,{data:l})})]}),e.jsx("svg",{className:"w-4 h-4 text-army-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})},s.id)})})]},"products"):null}function De({queries:t,queriesDatalistId:r}){return t.length?e.jsx("datalist",{id:r,children:t.map(a=>a?e.jsx("option",{value:a.text},a.text):null)}):null}function He({term:t}){return t.current?e.jsx("div",{className:"py-12 text-center",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"w-16 h-16 bg-army-100 rounded-full flex items-center justify-center mb-4",children:e.jsx("svg",{className:"w-8 h-8 text-army-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No results found"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["No results found for ",e.jsx("q",{className:"font-medium text-army-600",children:t.current})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Try searching for coffee, gear, or check your spelling"})]})}):null}function Be(){var l,i;const t=V({key:"search"}),r=m.useRef(""),a=m.useRef(null);(t==null?void 0:t.state)==="loading"&&(r.current=String(((l=t.formData)==null?void 0:l.get("q"))||"")),m.useEffect(()=>{a.current||(a.current=document.querySelector('input[type="search"]'))},[]);const{items:s,total:o}=((i=t==null?void 0:t.data)==null?void 0:i.result)??we();return{items:s,total:o,inputRef:a,term:r,fetcher:t}}const D="bigriver_utm_params",H="bigriver_utm_expiry",ze=24;function Q(t){const r={};return["utm_source","utm_medium","utm_campaign","utm_content","utm_term","utm_id"].forEach(s=>{const o=t.get(s);o&&(r[s]=o)}),r}function Ge(t){if(!(typeof window>"u"))try{if(Object.keys(t).length>0){const r=Date.now()+ze*60*60*1e3;sessionStorage.setItem(D,JSON.stringify(t)),sessionStorage.setItem(H,r.toString()),console.log("UTM parameters stored:",t)}}catch(r){console.warn("Failed to store UTM parameters:",r)}}function qe(){if(typeof window>"u")return null;try{const t=sessionStorage.getItem(D),r=sessionStorage.getItem(H);return!t||!r?null:Date.now()>parseInt(r)?(We(),null):JSON.parse(t)}catch(t){return console.warn("Failed to retrieve UTM parameters:",t),null}}function We(){if(!(typeof window>"u"))try{sessionStorage.removeItem(D),sessionStorage.removeItem(H)}catch(t){console.warn("Failed to clear UTM parameters:",t)}}function $e(t){if(t){const r=Q(t);if(Object.keys(r).length>0)return Ge(r),r}return qe()||{}}function Ve(t,r){const a=new URL(t,window.location.origin);return Object.entries(r).forEach(([s,o])=>{o&&a.searchParams.set(s,o)}),a.toString()}function Ye(t){return{campaign_source:t.utm_source,campaign_medium:t.utm_medium,campaign_name:t.utm_campaign,campaign_content:t.utm_content,campaign_term:t.utm_term,campaign_id:t.utm_id}}function Ke({to:t,utmParams:r,children:a,className:s,onClick:o,external:l=!1}){if(l){let n=t;return typeof window<"u"&&(n=Ve(t,r)),e.jsx("a",{href:n,className:s,onClick:o,target:"_blank",rel:"noopener noreferrer",children:a})}let i=t;if(typeof window<"u"){const n=new URL(t,window.location.origin);Object.entries(r).forEach(([c,u])=>{u&&n.searchParams.set(c,u)}),i=n.pathname+n.search}return e.jsx(M,{to:i,className:s,onClick:o,children:a})}const W={PopupCTA:({children:t,className:r,to:a="/collections/all"})=>e.jsx(Ke,{to:a,utmParams:{utm_source:"website",utm_medium:"popup",utm_campaign:"promo_popup",utm_content:"shop_coffee_button"},className:r,children:t})};function Je({isOpen:t,onClose:r}){const[a,s]=m.useState(!1),[o,l]=m.useState(!1);return m.useEffect(()=>{l(!0);const i=()=>{s(window.innerWidth<768)};return i(),window.addEventListener("resize",i),()=>window.removeEventListener("resize",i)},[]),m.useEffect(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),!t||!o?null:e.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[e.jsx("div",{className:"absolute inset-0 backdrop-blur-sm",onClick:r}),e.jsxs("div",{className:"relative z-10 max-w-4xl mx-4 max-h-[90vh] overflow-hidden",children:[e.jsx("button",{onClick:r,className:"absolute top-4 right-4 z-20 w-10 h-10 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110","aria-label":"Close popup",children:e.jsx("svg",{className:"w-6 h-6 text-gray-800",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),e.jsxs("div",{className:"relative bg-white rounded-lg shadow-2xl overflow-hidden border-4 border-white",children:[!a&&e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:"/desktoppopup.webp",alt:"Special Offer",className:"w-full h-auto max-h-[80vh] object-contain"}),e.jsx("div",{className:"absolute bottom-6 left-6",children:e.jsxs(W.PopupCTA,{className:"inline-flex items-center px-8 py-4 bg-army-600 hover:bg-army-700 font-semibold rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl",children:[e.jsx("span",{className:"text-white",children:"Shop Coffee"}),e.jsx("svg",{className:"w-5 h-5 ml-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})})]}),a&&e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:"/mobilepopup.webp",alt:"Special Offer",className:"w-full h-auto max-h-[80vh] object-contain"}),e.jsx("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2",children:e.jsxs(W.PopupCTA,{className:"inline-flex items-center px-6 py-3 bg-army-600 hover:bg-army-700 font-semibold rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl text-sm",children:[e.jsx("span",{className:"text-white",children:"Shop Coffee"}),e.jsx("svg",{className:"w-4 h-4 ml-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})})]})]})]})]})}function X(){const[t]=ce(),[r,a]=m.useState({}),[s,o]=m.useState(!0);return m.useEffect(()=>{const l=Q(t),i=$e(t);a(i),o(!1),Object.keys(l).length>0&&console.log("UTM parameters captured from URL:",l),Object.keys(i).length>0&&console.log("Active UTM parameters:",i)},[t]),{utmParams:r,isLoading:s,hasUTM:Object.keys(r).length>0}}function Qe(){const{utmParams:t,hasUTM:r}=X();return m.useEffect(()=>{r&&typeof window<"u"&&(window.gtag&&(window.gtag("event","traffic_source_identified",{event_category:"Traffic Attribution",event_label:`${t.utm_source} / ${t.utm_medium}`,traffic_source:t.utm_source,traffic_medium:t.utm_medium,traffic_campaign:t.utm_campaign,traffic_content:t.utm_content,traffic_term:t.utm_term,custom_parameter_1:t.utm_source,custom_parameter_2:t.utm_medium}),Z(t.utm_source,t.utm_medium)&&(console.log("🎯 SOCIAL MEDIA TRAFFIC DETECTED:",{platform:t.utm_source,campaign:t.utm_campaign,content:t.utm_content,timestamp:new Date().toISOString(),url:window.location.href}),window.gtag("event","social_media_traffic",{event_category:"Social Media Attribution",event_label:t.utm_source,social_platform:t.utm_source,social_campaign:t.utm_campaign,social_content:t.utm_content})),ee(t.utm_medium)&&(console.log("💰 PAID ADVERTISING TRAFFIC DETECTED:",{source:t.utm_source,medium:t.utm_medium,campaign:t.utm_campaign,term:t.utm_term,timestamp:new Date().toISOString()}),window.gtag("event","paid_advertising_traffic",{event_category:"Paid Advertising Attribution",event_label:`${t.utm_source} - ${t.utm_campaign}`,ad_source:t.utm_source,ad_medium:t.utm_medium,ad_campaign:t.utm_campaign,ad_term:t.utm_term}))),console.log("📊 TRAFFIC SOURCE ATTRIBUTION:",{source:t.utm_source,medium:t.utm_medium,campaign:t.utm_campaign,content:t.utm_content,term:t.utm_term,type:Xe(t.utm_source,t.utm_medium),timestamp:new Date().toISOString(),page:window.location.pathname}))},[r,t]),null}function Z(t,r){if(!t&&!r)return!1;const a=["facebook","instagram","twitter","linkedin","tiktok","snapchat","pinterest","youtube","reddit"],s=["social","social-media","social_media"];return a.includes((t==null?void 0:t.toLowerCase())||"")||s.includes((r==null?void 0:r.toLowerCase())||"")}function ee(t){return t?["cpc","ppc","paid","ads","advertising","paid-social","paid_social","display","banner"].includes(t.toLowerCase()):!1}function Xe(t,r){return Z(t,r)?"Social Media":ee(r)?"Paid Advertising":r==="email"?"Email Marketing":r==="referral"?"Referral":r==="organic"?"Organic Search":"Other"}function Ze(){return m.useEffect(()=>{const t=()=>{["/headerlogo.svg","/hpheronew.svg","/mobilepopup.webp","/brewedforwild.webp","/brewedforwildmobile.webp"].forEach(n=>{const c=document.createElement("link");c.rel="preload",c.as="image",c.href=n,document.head.appendChild(c)})},r=()=>{document.querySelectorAll("img:not([loading])").forEach((n,c)=>{c<3?n.setAttribute("loading","eager"):n.setAttribute("loading","lazy")})},a=()=>{if("IntersectionObserver"in window){const i=new IntersectionObserver(n=>{n.forEach(c=>{if(c.isIntersecting){const u=c.target;u.dataset.src&&(u.src=u.dataset.src,u.removeAttribute("data-src"),i.unobserve(u))}})});document.querySelectorAll("img[data-src]").forEach(n=>{i.observe(n)})}},s=()=>{document.querySelectorAll("video").forEach(n=>{n.hasAttribute("preload")||n.setAttribute("preload","metadata"),"loading"in HTMLVideoElement.prototype&&n.setAttribute("loading","lazy"),n.hasAttribute("data-optimized")||(new IntersectionObserver(u=>{u.forEach(d=>{d.isIntersecting?n.hasAttribute("autoplay")&&n.play().catch(()=>{}):n.pause()})},{threshold:.3,rootMargin:"50px"}).observe(n),n.setAttribute("data-optimized","true"))})},o=()=>{document.querySelectorAll("script[src]:not([defer]):not([async])").forEach(n=>{const c=n.getAttribute("src");c&&!c.includes("gtag")&&!c.includes("analytics")&&n.setAttribute("defer","")})},l=()=>{const i=document.createElement("style");i.textContent=`
        @font-face {
          font-display: swap;
        }
      `,document.head.appendChild(i)};return t(),r(),a(),s(),o(),l(),()=>{}},[]),null}function et({children:t}){const r=Y(),[a,s]=m.useState(!0);return m.useEffect(()=>{if(r.pathname){s(!1);const o=setTimeout(()=>s(!0),100);return()=>clearTimeout(o)}},[r.pathname]),e.jsx("div",{className:`transition-all duration-300 ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-2"}`,style:{minHeight:"200px"},children:t})}function tt({cart:t,children:r=null,footer:a,header:s,isLoggedIn:o,publicStoreDomain:l}){const[n,c]=m.useState(!1);m.useEffect(()=>{},[!1]);const u=()=>{c(!1),localStorage.setItem("bigriver-promo-popup-seen","true")};return e.jsxs(L.Provider,{children:[e.jsx(rt,{cart:t}),e.jsx(st,{}),e.jsx(it,{header:s,publicStoreDomain:l}),s&&e.jsx(Te,{header:s,cart:t,isLoggedIn:o,publicStoreDomain:l}),e.jsx("main",{children:e.jsx(et,{children:r})}),e.jsx(ke,{footer:a,header:s,publicStoreDomain:l}),e.jsx(Qe,{}),e.jsx(Ze,{}),e.jsx(Je,{isOpen:n,onClose:u})]})}function rt({cart:t}){return e.jsx(L,{type:"cart",heading:"Shopping Cart",children:e.jsx(m.Suspense,{fallback:e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"}),e.jsx("span",{className:"ml-3 text-army-600 font-medium",children:"Loading cart..."})]}),children:e.jsx(A,{resolve:t,children:r=>e.jsx(xe,{cart:r,layout:"aside"})})})})}function st(){const t=m.useId();return e.jsx(L,{type:"search",heading:"SEARCH",children:e.jsxs("div",{className:"h-full flex flex-col space-y-6",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(Le,{children:({fetchResults:r,goToSearch:a,inputRef:s})=>e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"relative",children:e.jsx("input",{name:"q",onChange:r,onFocus:r,placeholder:"Search for coffee, gear, or anything...",ref:s,type:"search",list:t,className:"w-full px-4 py-3 bg-white border border-army-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 text-gray-900 placeholder-gray-500"})}),e.jsx("button",{onClick:a,className:"w-full bg-army-600 text-white py-3 px-4 rounded-lg hover:bg-army-700 transition-colors duration-200 font-medium",children:"Search All Results"})]})})}),e.jsx("div",{className:"flex-1 overflow-y-auto",children:e.jsx(y,{children:({items:r,total:a,term:s,state:o,closeSearch:l})=>{const{articles:i,collections:n,pages:c,products:u,queries:d}=r;return o==="loading"&&s.current?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"}),e.jsx("span",{className:"ml-3 text-army-600 font-medium",children:"Searching..."})]}):a?e.jsxs("div",{className:"space-y-6",children:[e.jsx(y.Queries,{queries:d,queriesDatalistId:t}),e.jsx(y.Products,{products:u,closeSearch:l,term:s}),e.jsx(y.Collections,{collections:n,closeSearch:l,term:s}),e.jsx(y.Pages,{pages:c,closeSearch:l,term:s}),e.jsx(y.Articles,{articles:i,closeSearch:l,term:s}),s.current&&a?e.jsx("div",{className:"pt-4 border-t border-army-200",children:e.jsxs(M,{onClick:l,to:`${R}?q=${s.current}`,className:"block w-full p-4 bg-army-600 text-white rounded-lg hover:bg-army-700 transition-colors duration-200 text-center font-medium",children:["View all ",a,' results for "',s.current,'"']})}):null]}):e.jsx(y.Empty,{term:s})}})})]})})}function it({header:t,publicStoreDomain:r}){var a;return t.menu&&((a=t.shop.primaryDomain)==null?void 0:a.url)&&e.jsx(L,{type:"mobile",heading:"MENU",children:e.jsx(K,{menu:t.menu,viewport:"mobile",primaryDomainUrl:t.shop.primaryDomain.url,publicStoreDomain:r})})}function nt(){const{subscribe:t,register:r}=$(),[a,s]=m.useState(!1),{utmParams:o,hasUTM:l}=X();return m.useEffect(()=>{const n=setTimeout(()=>{if(typeof window.gtag=="function"){s(!0);return}const c=document.createElement("script");c.async=!0,c.src="https://www.googletagmanager.com/gtag/js?id=G-KWTBMRWDGP",c.onload=()=>{window.dataLayer=window.dataLayer||[];function u(...d){window.dataLayer.push(d)}window.gtag=u,u("js",new Date),u("config","G-KWTBMRWDGP",{send_page_view:!0,allow_google_signals:!0,allow_ad_personalization_signals:!0,custom_map:{custom_parameter_1:"coffee_type",custom_parameter_2:"subscription_type"}}),s(!0),console.log("Google Analytics loaded successfully for Big River Coffee")},c.onerror=()=>{console.error("Failed to load Google Analytics")},document.head.appendChild(c)},100);return()=>clearTimeout(n)},[]),m.useEffect(()=>{a&&(r("Google Analytics 4"),t("page_viewed",i=>{if(typeof window<"u"&&window.gtag){const n={page_title:i.pageTitle||document.title,page_location:i.url||window.location.href,page_path:i.path||window.location.pathname};l&&(n.campaign_source=o.utm_source,n.campaign_medium=o.utm_medium,n.campaign_name=o.utm_campaign,n.campaign_content=o.utm_content,n.campaign_term=o.utm_term,n.campaign_id=o.utm_id,n.traffic_source=o.utm_source,n.traffic_medium=o.utm_medium,n.traffic_campaign=o.utm_campaign),window.gtag("event","page_view",n),l&&console.log("🎯 Traffic Source Tracked:",{source:o.utm_source,medium:o.utm_medium,campaign:o.utm_campaign,full_data:n})}}),t("product_viewed",i=>{var n;if(typeof window<"u"&&window.gtag){const u=(i.products||[])[0];if(u){const d=typeof u.price=="object"?parseFloat(((n=u.price)==null?void 0:n.amount)||"0"):parseFloat(u.price||"0");window.gtag("event","view_item",{currency:i.currency||"USD",value:d,items:[{item_id:u.id,item_name:u.title,item_category:u.productType||"Coffee",item_variant:i.variantTitle,price:d,quantity:1}]})}}}),t("product_added_to_cart",i=>{var n,c;if(typeof window<"u"&&window.gtag){const d=(i.products||[])[0];if(d){const h=typeof d.price=="object"?parseFloat(((n=d.price)==null?void 0:n.amount)||"0"):parseFloat(d.price||"0"),g=i.quantity||1,p={currency:typeof d.price=="object"&&((c=d.price)==null?void 0:c.currencyCode)||"USD",value:h*g,items:[{item_id:d.id,item_name:d.title,item_category:d.productType||"Coffee",item_variant:i.variantTitle,price:h,quantity:g}]};l&&(p.traffic_source=o.utm_source,p.traffic_medium=o.utm_medium,p.traffic_campaign=o.utm_campaign),window.gtag("event","add_to_cart",p)}}}),t("cart_viewed",i=>{var n,c,u,d,h,g,p,j;if(typeof window<"u"&&window.gtag){const P=(((c=(n=i.cart)==null?void 0:n.lines)==null?void 0:c.nodes)||[]).map(f=>{var b,T,_,x,v,N,k,S,C;return{item_id:(T=(b=f.merchandise)==null?void 0:b.product)==null?void 0:T.id,item_name:(x=(_=f.merchandise)==null?void 0:_.product)==null?void 0:x.title,item_category:((N=(v=f.merchandise)==null?void 0:v.product)==null?void 0:N.productType)||"Coffee",item_variant:(k=f.merchandise)==null?void 0:k.title,price:parseFloat(((C=(S=f.merchandise)==null?void 0:S.price)==null?void 0:C.amount)||"0"),quantity:f.quantity||1}});window.gtag("event","view_cart",{currency:((h=(d=(u=i.cart)==null?void 0:u.cost)==null?void 0:d.totalAmount)==null?void 0:h.currencyCode)||"USD",value:parseFloat(((j=(p=(g=i.cart)==null?void 0:g.cost)==null?void 0:p.totalAmount)==null?void 0:j.amount)||"0"),items:P})}}),t("custom_checkout_started",i=>{var n,c,u,d,h,g,p,j;if(typeof window<"u"&&window.gtag){const P=(((c=(n=i.cart)==null?void 0:n.lines)==null?void 0:c.nodes)||[]).map(f=>{var b,T,_,x,v,N,k,S,C;return{item_id:(T=(b=f.merchandise)==null?void 0:b.product)==null?void 0:T.id,item_name:(x=(_=f.merchandise)==null?void 0:_.product)==null?void 0:x.title,item_category:((N=(v=f.merchandise)==null?void 0:v.product)==null?void 0:N.productType)||"Coffee",item_variant:(k=f.merchandise)==null?void 0:k.title,price:parseFloat(((C=(S=f.merchandise)==null?void 0:S.price)==null?void 0:C.amount)||"0"),quantity:f.quantity||1}});window.gtag("event","begin_checkout",{currency:((h=(d=(u=i.cart)==null?void 0:u.cost)==null?void 0:d.totalAmount)==null?void 0:h.currencyCode)||"USD",value:parseFloat(((j=(p=(g=i.cart)==null?void 0:g.cost)==null?void 0:p.totalAmount)==null?void 0:j.amount)||"0"),items:P})}}),t("custom_purchase",i=>{var n,c,u,d,h,g,p,j,I,P,f,b;if(typeof window<"u"&&window.gtag){const T=((c=(n=i.order)==null?void 0:n.lineItems)==null?void 0:c.map(x=>{var v,N,k,S,C,B,z,G,q;return{item_id:(N=(v=x.variant)==null?void 0:v.product)==null?void 0:N.id,item_name:(S=(k=x.variant)==null?void 0:k.product)==null?void 0:S.title,item_category:((B=(C=x.variant)==null?void 0:C.product)==null?void 0:B.productType)||"Coffee",item_variant:(z=x.variant)==null?void 0:z.title,price:parseFloat(((q=(G=x.variant)==null?void 0:G.price)==null?void 0:q.amount)||"0"),quantity:x.quantity||1}}))||[],_=l?Ye(o):{};window.gtag("event","purchase",{transaction_id:(u=i.order)==null?void 0:u.id,currency:((h=(d=i.order)==null?void 0:d.totalPrice)==null?void 0:h.currencyCode)||"USD",value:parseFloat(((p=(g=i.order)==null?void 0:g.totalPrice)==null?void 0:p.amount)||"0"),items:T,coffee_subscription:((I=(j=i.order)==null?void 0:j.lineItems)==null?void 0:I.some(x=>{var v;return(v=x.sellingPlan)==null?void 0:v.id}))||!1,..._}),l&&console.log("Purchase conversion with UTM attribution:",{transaction_id:(P=i.order)==null?void 0:P.id,value:parseFloat(((b=(f=i.order)==null?void 0:f.totalPrice)==null?void 0:b.amount)||"0"),utm_data:_})}}))},[a,t,r]),null}function ot(){return m.useEffect(()=>{if(typeof window>"u"||window.dataLayer)return;window.dataLayer=window.dataLayer||[],window.dataLayer.push({"gtm.start":new Date().getTime(),event:"gtm.js"});const t=document.createElement("script");return t.async=!0,t.src="https://www.googletagmanager.com/gtm.js?id=GTM-WXN2JD85",t.onload=()=>{console.log("Google Tag Manager loaded")},t.onerror=()=>{console.error("Failed to load Google Tag Manager")},document.head.appendChild(t),()=>{t.parentNode&&t.parentNode.removeChild(t)}},[]),null}const wt=({formMethod:t,currentUrl:r,nextUrl:a})=>!!(t&&t!=="GET"||r.toString()===a.toString());function yt(){return[{rel:"preconnect",href:"https://cdn.shopify.com"},{rel:"preconnect",href:"https://shop.app"},{rel:"preconnect",href:"https://www.googletagmanager.com"},{rel:"preconnect",href:"https://www.google-analytics.com"},{rel:"preconnect",href:"https://fonts.googleapis.com"},{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"},{rel:"stylesheet",href:"https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Oswald:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700;800&display=swap"},{rel:"preload",href:"/headerlogo.svg",as:"image",type:"image/svg+xml"},{rel:"preload",href:"/hpheronew.svg",as:"image",type:"image/svg+xml"},{rel:"icon",type:"image/svg+xml",href:ve}]}function vt({children:t}){const r=ie(),a=le("root"),s=Y(),o=s.pathname==="/"||s.pathname==="";return e.jsxs("html",{lang:"en",children:[e.jsxs("head",{children:[e.jsx("meta",{charSet:"utf-8"}),e.jsx("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),e.jsx("meta",{name:"theme-color",content:"#4a5d23"}),e.jsx("meta",{name:"format-detection",content:"telephone=no"}),e.jsx("meta",{httpEquiv:"x-dns-prefetch-control",content:"on"}),e.jsx("link",{rel:"dns-prefetch",href:"//cdn.shopify.com"}),e.jsx("link",{rel:"dns-prefetch",href:"//www.googletagmanager.com"}),e.jsx("link",{rel:"dns-prefetch",href:"//www.google-analytics.com"}),e.jsx("link",{rel:"icon",type:"image/x-icon",href:"/Logo_Official.ico?v=1"}),e.jsx("link",{rel:"shortcut icon",type:"image/x-icon",href:"/Logo_Official.ico?v=1"}),e.jsx("link",{rel:"apple-touch-icon",href:"/Logo_Official.ico?v=1"}),e.jsx("link",{rel:"stylesheet",href:je}),e.jsx("link",{rel:"stylesheet",href:be}),e.jsx("link",{rel:"stylesheet",href:_e}),e.jsx("link",{rel:"stylesheet",href:Ne}),e.jsx(ue,{}),e.jsx(de,{})]}),e.jsxs("body",{className:o?"homepage":"",children:[a?e.jsxs(ne.Provider,{cart:a.cart,shop:a.shop,consent:a.consent,children:[e.jsx(ot,{}),e.jsx(nt,{}),e.jsx(tt,{...a,children:t})]}):t,e.jsx(me,{nonce:r}),e.jsx(he,{nonce:r})]})]})}const jt=te(function(){return e.jsx(pe,{})}),bt=re(function(){var o;const r=fe();let a="Unknown error",s=500;return ge(r)?(a=((o=r==null?void 0:r.data)==null?void 0:o.message)??r.data,s=r.status):r instanceof Error&&(a=r.message),e.jsxs("div",{className:"route-error",children:[e.jsx("h1",{children:"Oops"}),e.jsx("h2",{children:s}),a&&e.jsx("fieldset",{children:e.jsx("pre",{children:a})})]})});export{bt as ErrorBoundary,vt as Layout,jt as default,yt as links,wt as shouldRevalidate};
