import{w as C}from"./with-props-ZU_SrDpf.js";import{j as e}from"./jsx-runtime-D5QEUsP9.js";import{c as w,b as N,u as L,r as u}from"./chunk-D4RADZKF-DNKU_WG6.js";import{s as P}from"./index-BX9xgbR8.js";import{P as M}from"./ProductCard-Og2ifzIn.js";import"./variants-J2GcD6hP.js";import"./AddToCartButton-C9QeiPEA.js";import"./Image-BhFgnyoc.js";import"./Money-BAfw1cBI.js";const R=[{label:"All Roasts",value:"all"},{label:"Light Roast",value:"light"},{label:"Medium Roast",value:"medium"},{label:"Dark Roast",value:"dark"}],S=[{label:"All Prices",value:"all"},{label:"Under $25",value:"under-25"},{label:"$25 - $50",value:"25-50"},{label:"Over $50",value:"over-50"}];function b({isOpen:l=!0,onToggle:a,activeSection:d="coffee"}){if(d!=="coffee")return null;const g=w(),[i]=N(),p=i.get("roast")||"all",n=i.get("price")||"all";function m(s,o){const c=new URLSearchParams(i);o&&o!=="all"?c.set(s,o):c.delete(s),g(`?${c.toString()}`)}function x(){const s=new URLSearchParams,o=i.get("section");o&&s.set("section",o),g(`?${s.toString()}`)}const v=p!=="all"||n!=="all";return e.jsxs("div",{className:"bg-amber-600 rounded-xl shadow-sm border border-amber-500 overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 bg-amber-700 border-b border-amber-500",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("svg",{className:"w-5 h-5 mr-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})}),e.jsx("h2",{className:"text-lg font-bold text-white",children:"Filters"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[v&&e.jsx("button",{onClick:x,className:"text-sm text-white hover:text-gray-200 font-medium transition-colors",children:"Clear All"}),a&&e.jsx("button",{onClick:a,className:"lg:hidden p-1 text-gray-200 hover:text-white transition-colors",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),e.jsx("div",{className:`transition-all duration-300 ${l?"max-h-screen opacity-100":"max-h-0 opacity-0 overflow-hidden"}`,children:e.jsxs("div",{className:"p-6 space-y-8",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm font-semibold text-white mb-4 flex items-center",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 18.657A8 8 0 716.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"})}),"Roast Level"]}),e.jsx("div",{className:"space-y-3",children:R.map(s=>e.jsxs("label",{className:"flex items-center cursor-pointer group",children:[e.jsx("input",{type:"radio",name:"roast",checked:p===s.value,onChange:()=>m("roast",s.value),className:"h-4 w-4 text-army-600 border-amber-400 bg-amber-500 focus:ring-army-500 focus:ring-offset-amber-600"}),e.jsx("span",{className:"ml-3 text-sm text-white group-hover:text-gray-200 transition-colors",children:s.label})]},s.value))})]}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm font-semibold text-white mb-4 flex items-center",children:[e.jsx("svg",{className:"w-4 h-4 mr-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),"Price Range"]}),e.jsx("div",{className:"space-y-3",children:S.map(s=>e.jsxs("label",{className:"flex items-center cursor-pointer group",children:[e.jsx("input",{type:"radio",name:"price-range",checked:n===s.value,onChange:()=>m("price",s.value),className:"h-4 w-4 text-army-600 border-amber-400 bg-amber-500 focus:ring-army-500 focus:ring-offset-amber-600"}),e.jsx("span",{className:"ml-3 text-sm text-white group-hover:text-gray-200 transition-colors",children:s.label})]},s.value))})]})]})})]})}const H=({data:l})=>[{title:`${(l==null?void 0:l.collection.title)??"Coffee"} | Big River Coffee`},{description:(l==null?void 0:l.collection.description)||"Premium coffee for adventurers - ethically sourced and expertly roasted"}],U=C(function(){const{collection:a}=L(),[d]=N(),g=w(),[i,p]=u.useState(a.products.nodes),[n,m]=u.useState("grid"),[x,v]=u.useState("featured"),[s,o]=u.useState(!1),c=d.get("type")||"all",f=d.get("roast")||"all",j=d.get("origin")||"",y=d.get("price")||"all";u.useEffect(()=>{let t=[...a.products.nodes];switch(c!=="all"&&(t=t.filter(r=>r.title.toLowerCase().includes(c.replace("-"," ")))),f!=="all"&&(t=t.filter(r=>r.title.toLowerCase().includes(f))),j&&(t=t.filter(r=>r.title.toLowerCase().includes(j.replace("-"," ")))),x){case"price-low":t.sort((r,h)=>parseFloat(r.priceRange.minVariantPrice.amount)-parseFloat(h.priceRange.minVariantPrice.amount));break;case"price-high":t.sort((r,h)=>parseFloat(h.priceRange.minVariantPrice.amount)-parseFloat(r.priceRange.minVariantPrice.amount));break;case"name":t.sort((r,h)=>r.title.localeCompare(h.title));break}p(t)},[a.products.nodes,c,f,j,y,x]);const k=t=>{v(t)};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsxs("div",{className:"relative h-80 bg-army-900 overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-cover bg-center",style:{backgroundImage:"url(/tent.svg)",backgroundSize:"cover",backgroundPosition:"center"}}),e.jsx("div",{className:"absolute inset-0 bg-army-900/70"}),e.jsx("div",{className:"relative z-10 container-clean h-full flex items-center",children:e.jsxs("div",{className:"text-white",children:[e.jsx("h1",{className:"text-5xl md:text-6xl font-black mb-4",children:a.title}),e.jsx("p",{className:"text-xl md:text-2xl text-gray-200 max-w-2xl leading-relaxed",children:a.description||"Premium coffee for every adventure - ethically sourced and expertly roasted"}),e.jsxs("div",{className:"flex items-center mt-6 space-x-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:a.products.nodes.length}),e.jsx("div",{className:"text-sm text-gray-300",children:"Products"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:"100%"}),e.jsx("div",{className:"text-sm text-gray-300",children:"Ethical"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:"48hr"}),e.jsx("div",{className:"text-sm text-gray-300",children:"Fresh"})]})]})]})})]}),e.jsx("div",{className:"container-clean py-12",children:e.jsxs("div",{className:"flex flex-col lg:flex-row gap-8",children:[e.jsx("div",{className:"hidden lg:block w-80 flex-shrink-0",children:e.jsx("div",{className:"sticky top-8",children:e.jsx(b,{})})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("button",{onClick:()=>o(!s),className:"lg:hidden btn-secondary px-4 py-2 text-sm",children:[e.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})}),"Filters"]}),e.jsxs("span",{className:"text-gray-600",children:[i.length," ",i.length===1?"product":"products"]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("select",{value:x,onChange:t=>k(t.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-army-500",children:[e.jsx("option",{value:"featured",children:"Featured"}),e.jsx("option",{value:"name",children:"Name A-Z"}),e.jsx("option",{value:"price-low",children:"Price: Low to High"}),e.jsx("option",{value:"price-high",children:"Price: High to Low"})]}),e.jsxs("div",{className:"flex border border-gray-300 rounded-lg overflow-hidden",children:[e.jsx("button",{onClick:()=>m("grid"),className:`p-2 ${n==="grid"?"bg-amber-600 text-white":"bg-white text-gray-600"}`,children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})})}),e.jsx("button",{onClick:()=>m("list"),className:`p-2 ${n==="list"?"bg-amber-600 text-white":"bg-white text-gray-600"}`,children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 10h16M4 14h16M4 18h16"})})})]})]})]}),s&&e.jsx("div",{className:"lg:hidden mb-8 p-6 bg-white rounded-xl shadow-sm border border-gray-200",children:e.jsx(b,{})}),e.jsx("div",{className:`grid gap-6 ${n==="grid"?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:i.map((t,r)=>e.jsx(M,{product:t,loading:r<6?"eager":"lazy",viewMode:n},t.id))}),i.length===0&&e.jsxs("div",{className:"text-center py-16",children:[e.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6",children:e.jsx("svg",{className:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No products found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Try adjusting your filters to see more results."}),e.jsx("button",{onClick:()=>g(window.location.pathname),className:"btn-primary",children:"Clear All Filters"})]})]})]})}),e.jsx(P.CollectionView,{data:{collection:{id:a.id,handle:a.handle}}})]})});export{U as default,H as meta};
